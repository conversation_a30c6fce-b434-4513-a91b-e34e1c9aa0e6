<?php
// Database Connection Test Script
echo "<h2>HR System Database Connection Test</h2>";

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "jelodb";

echo "<h3>Testing MySQL Connection...</h3>";

// Test basic MySQL connection
$conn = new mysqli($servername, $username, $password);
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ MySQL Connection Failed: " . $conn->connect_error . "</p>";
    echo "<h4>Troubleshooting Steps:</h4>";
    echo "<ol>";
    echo "<li>Make sure XAMPP is running</li>";
    echo "<li>Start MySQL service in XAMPP Control Panel</li>";
    echo "<li>Check if MySQL is running on port 3306</li>";
    echo "</ol>";
    exit;
} else {
    echo "<p style='color: green;'>✅ MySQL Connection Successful!</p>";
}

// Test if database exists
$result = $conn->query("SHOW DATABASES LIKE 'jelodb'");
if ($result->num_rows == 0) {
    echo "<p style='color: orange;'>⚠️ Database 'jelodb' does not exist. Creating it now...</p>";
    
    // Create database
    if ($conn->query("CREATE DATABASE jelodb") === TRUE) {
        echo "<p style='color: green;'>✅ Database 'jelodb' created successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Error creating database: " . $conn->error . "</p>";
        exit;
    }
} else {
    echo "<p style='color: green;'>✅ Database 'jelodb' exists!</p>";
}

// Connect to the specific database
$conn->select_db($dbname);

// Check if users table exists
$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows == 0) {
    echo "<p style='color: orange;'>⚠️ Users table does not exist. Creating tables...</p>";
    
    // Read and execute SQL file content
    $sql_content = file_get_contents('jelodb.sql');
    
    // Remove CREATE DATABASE and USE statements since we're already connected
    $sql_content = preg_replace('/CREATE DATABASE.*?;/', '', $sql_content);
    $sql_content = preg_replace('/USE.*?;/', '', $sql_content);
    
    // Split SQL into individual statements
    $statements = explode(';', $sql_content);
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            if ($conn->query($statement) === TRUE) {
                $success_count++;
            } else {
                $error_count++;
                echo "<p style='color: red;'>❌ Error executing: " . substr($statement, 0, 50) . "... - " . $conn->error . "</p>";
            }
        }
    }
    
    echo "<p style='color: green;'>✅ Executed $success_count SQL statements successfully!</p>";
    if ($error_count > 0) {
        echo "<p style='color: orange;'>⚠️ $error_count statements had errors.</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Users table exists!</p>";
}

// Test user accounts
echo "<h3>Testing User Accounts...</h3>";
$result = $conn->query("SELECT username, role FROM users");

if ($result->num_rows > 0) {
    echo "<p style='color: green;'>✅ Found " . $result->num_rows . " user accounts:</p>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th style='padding: 8px; background: #f0f0f0;'>Username</th><th style='padding: 8px; background: #f0f0f0;'>Role</th><th style='padding: 8px; background: #f0f0f0;'>Login URL</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        $login_url = "auth/login.php";
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['role']) . "</td>";
        echo "<td style='padding: 8px;'><a href='$login_url' target='_blank'>Login</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Default Login Credentials:</h4>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> admin / admin123</li>";
    echo "<li><strong>HR Head:</strong> hrhead / hrhead123</li>";
    echo "<li><strong>HR Staff:</strong> hrstaff / hrstaff123</li>";
    echo "<li><strong>Employee:</strong> employee1 / emp123</li>";
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>❌ No user accounts found!</p>";
    echo "<p>The users table exists but is empty. This might indicate an import issue.</p>";
}

// Test database connection from config file
echo "<h3>Testing Config File Connection...</h3>";
include 'config/db.php';
if ($conn) {
    echo "<p style='color: green;'>✅ Config file database connection successful!</p>";
} else {
    echo "<p style='color: red;'>❌ Config file database connection failed!</p>";
}

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>If all tests pass, try logging in with the credentials above</li>";
echo "<li>If login fails, check the login.php file for any errors</li>";
echo "<li>Make sure XAMPP Apache and MySQL services are running</li>";
echo "<li>Access the system at: <a href='auth/login.php'>auth/login.php</a></li>";
echo "</ol>";

$conn->close();
?>
