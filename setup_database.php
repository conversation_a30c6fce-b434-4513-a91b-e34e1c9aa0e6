<?php
// Database Setup Script for HR System
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>HR System Database Setup</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .step { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
</style>";

// Step 1: Test MySQL Connection
echo "<div class='step'>";
echo "<h2>Step 1: Testing MySQL Connection</h2>";

$servername = "localhost";
$username = "root";
$password = "";

$conn = new mysqli($servername, $username, $password);

if ($conn->connect_error) {
    echo "<p class='error'>❌ Connection failed: " . $conn->connect_error . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP is installed and running</li>";
    echo "<li>Start MySQL service in XAMPP Control Panel</li>";
    echo "<li>Check if MySQL is running on port 3306</li>";
    echo "<li>Try restarting XAMPP as administrator</li>";
    echo "</ul>";
    exit;
} else {
    echo "<p class='success'>✅ MySQL connection successful!</p>";
}
echo "</div>";

// Step 2: Create Database
echo "<div class='step'>";
echo "<h2>Step 2: Creating Database</h2>";

$dbname = "jelodb";

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows > 0) {
    echo "<p class='warning'>⚠️ Database '$dbname' already exists. Dropping and recreating...</p>";
    $conn->query("DROP DATABASE $dbname");
}

if ($conn->query("CREATE DATABASE $dbname") === TRUE) {
    echo "<p class='success'>✅ Database '$dbname' created successfully!</p>";
} else {
    echo "<p class='error'>❌ Error creating database: " . $conn->error . "</p>";
    exit;
}

// Select the database
$conn->select_db($dbname);
echo "</div>";

// Step 3: Create Tables
echo "<div class='step'>";
echo "<h2>Step 3: Creating Tables</h2>";

$tables = [
    "users" => "CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('Admin','HR Head','HR Staff','Employee') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    "attendance_uploads" => "CREATE TABLE attendance_uploads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) NOT NULL,
        uploaded_by INT NOT NULL,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (uploaded_by) REFERENCES users(id)
    )",
    
    "attendance_records" => "CREATE TABLE attendance_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        upload_id INT NOT NULL,
        employee_id INT NOT NULL,
        date DATE NOT NULL,
        status ENUM('Present','Absent','On Leave') DEFAULT 'Present',
        FOREIGN KEY (upload_id) REFERENCES attendance_uploads(id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES users(id)
    )",
    
    "leave_requests" => "CREATE TABLE leave_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type ENUM('Vacation','Sick','Emergency','Other') NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        reason TEXT,
        status ENUM('Pending','Approved','Rejected') DEFAULT 'Pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )",
    
    "payroll" => "CREATE TABLE payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        status ENUM('Draft','Approved') DEFAULT 'Draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES users(id)
    )",
    
    "performance_reviews" => "CREATE TABLE performance_reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        score INT NOT NULL,
        feedback TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES users(id)
    )"
];

foreach ($tables as $table_name => $sql) {
    if ($conn->query($sql) === TRUE) {
        echo "<p class='success'>✅ Table '$table_name' created successfully!</p>";
    } else {
        echo "<p class='error'>❌ Error creating table '$table_name': " . $conn->error . "</p>";
    }
}
echo "</div>";

// Step 4: Insert Sample Users
echo "<div class='step'>";
echo "<h2>Step 4: Creating User Accounts</h2>";

$users = [
    ['admin', 'admin123', 'Admin'],
    ['hrhead', 'hrhead123', 'HR Head'],
    ['hrstaff', 'hrstaff123', 'HR Staff'],
    ['employee1', 'emp123', 'Employee'],
    ['employee2', 'emp123', 'Employee']
];

foreach ($users as $user) {
    $username = $user[0];
    $password = md5($user[1]); // Using MD5 for compatibility with existing code
    $role = $user[2];
    
    $stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $username, $password, $role);
    
    if ($stmt->execute()) {
        echo "<p class='success'>✅ User '$username' ($role) created successfully!</p>";
    } else {
        echo "<p class='error'>❌ Error creating user '$username': " . $conn->error . "</p>";
    }
    $stmt->close();
}
echo "</div>";

// Step 5: Verify Setup
echo "<div class='step'>";
echo "<h2>Step 5: Verification</h2>";

// Check users table
$result = $conn->query("SELECT username, role, created_at FROM users ORDER BY role, username");

if ($result->num_rows > 0) {
    echo "<p class='success'>✅ Found " . $result->num_rows . " user accounts:</p>";
    echo "<table>";
    echo "<tr><th>Username</th><th>Role</th><th>Created</th><th>Password</th></tr>";
    
    $credentials = [
        'admin' => 'admin123',
        'hrhead' => 'hrhead123', 
        'hrstaff' => 'hrstaff123',
        'employee1' => 'emp123',
        'employee2' => 'emp123'
    ];
    
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . htmlspecialchars($row['role']) . "</td>";
        echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
        echo "<td>" . ($credentials[$row['username']] ?? 'Unknown') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>❌ No users found in database!</p>";
}

// Test database connection from config file
echo "<h3>Testing Config File Connection:</h3>";
try {
    include 'config/db.php';
    if (isset($conn) && !$conn->connect_error) {
        echo "<p class='success'>✅ Config file database connection works!</p>";
    } else {
        echo "<p class='error'>❌ Config file database connection failed!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error including config file: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Step 6: Next Steps
echo "<div class='step'>";
echo "<h2>Step 6: Ready to Use!</h2>";
echo "<p class='success'>🎉 Database setup completed successfully!</p>";

echo "<h3>Login Credentials:</h3>";
echo "<table>";
echo "<tr><th>Role</th><th>Username</th><th>Password</th><th>Action</th></tr>";
echo "<tr><td>Admin</td><td>admin</td><td>admin123</td><td><a href='auth/login.php' target='_blank'>Login</a></td></tr>";
echo "<tr><td>HR Head</td><td>hrhead</td><td>hrhead123</td><td><a href='auth/login.php' target='_blank'>Login</a></td></tr>";
echo "<tr><td>HR Staff</td><td>hrstaff</td><td>hrstaff123</td><td><a href='auth/login.php' target='_blank'>Login</a></td></tr>";
echo "<tr><td>Employee</td><td>employee1</td><td>emp123</td><td><a href='auth/login.php' target='_blank'>Login</a></td></tr>";
echo "<tr><td>Employee</td><td>employee2</td><td>emp123</td><td><a href='auth/login.php' target='_blank'>Login</a></td></tr>";
echo "</table>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test Login:</strong> <a href='auth/login.php' target='_blank'>Click here to go to login page</a></li>";
echo "<li><strong>Start with Admin:</strong> Use admin/admin123 to access all features</li>";
echo "<li><strong>Delete this file:</strong> Remove setup_database.php for security</li>";
echo "</ol>";

echo "<h3>Troubleshooting:</h3>";
echo "<ul>";
echo "<li>If login still fails, check the browser console for JavaScript errors</li>";
echo "<li>Make sure XAMPP Apache and MySQL services are running</li>";
echo "<li>Clear browser cache and cookies</li>";
echo "<li>Check that the URL is correct: http://localhost/hr_system_ready/auth/login.php</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>

<script>
// Auto-refresh every 30 seconds if there were errors
if (document.querySelector('.error')) {
    console.log('Errors detected, will not auto-refresh');
} else {
    console.log('Setup completed successfully!');
}
</script>
