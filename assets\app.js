/**
 * Modern HR System - JavaScript Utilities
 * Provides common functionality across the application
 */

// Global app object
window.HRSystem = {
    // Configuration
    config: {
        animationDuration: 300,
        toastDuration: 5000,
        debounceDelay: 300
    },

    // Initialize the application
    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAnimations();
    },

    // Setup global event listeners
    setupEventListeners() {
        // Handle sidebar toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sidebar-toggle')) {
                this.toggleSidebar();
            }
            
            if (e.target.matches('.mobile-menu-toggle')) {
                this.toggleMobileMenu();
            }
            
            if (e.target.matches('.mobile-overlay')) {
                this.closeMobileMenu();
            }
        });

        // Handle dropdown toggles
        document.addEventListener('click', (e) => {
            if (e.target.closest('.dropdown-toggle')) {
                e.preventDefault();
                const dropdown = e.target.closest('.dropdown');
                this.toggleDropdown(dropdown);
            } else {
                // Close all dropdowns when clicking outside
                this.closeAllDropdowns();
            }
        });

        // Handle modal triggers
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-target]')) {
                e.preventDefault();
                const modalId = e.target.getAttribute('data-modal-target');
                this.openModal(modalId);
            }
            
            if (e.target.matches('.modal-close') || e.target.matches('.modal-overlay')) {
                this.closeModal();
            }
        });

        // Handle tab switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.tab-button')) {
                e.preventDefault();
                this.switchTab(e.target);
            }
        });

        // Handle form enhancements
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-loading]')) {
                this.handleFormSubmit(e.target);
            }
        });
    },

    // Initialize components
    initializeComponents() {
        // Set active navigation items
        this.setActiveNavigation();
        
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize form validation
        this.initializeFormValidation();
    },

    // Setup animations
    setupAnimations() {
        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },

    // Sidebar functionality
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        const toggleIcon = document.getElementById('toggle-icon');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            
            if (toggleIcon) {
                toggleIcon.textContent = sidebar.classList.contains('collapsed') ? '›' : '‹';
            }
            
            // Store preference
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }
    },

    // Mobile menu functionality
    toggleMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.mobile-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('mobile-open');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    },

    closeMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.mobile-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('mobile-open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    },

    // Dropdown functionality
    toggleDropdown(dropdown) {
        const isActive = dropdown.classList.contains('active');
        
        // Close all dropdowns first
        this.closeAllDropdowns();
        
        // Toggle current dropdown
        if (!isActive) {
            dropdown.classList.add('active');
        }
    },

    closeAllDropdowns() {
        document.querySelectorAll('.dropdown.active').forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    },

    // Modal functionality
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Focus on first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    },

    closeModal() {
        document.querySelectorAll('.modal-overlay.active').forEach(modal => {
            modal.classList.remove('active');
        });
        document.body.style.overflow = '';
    },

    // Tab functionality
    switchTab(tabButton) {
        const tabContainer = tabButton.closest('.tabs');
        const targetId = tabButton.getAttribute('data-tab-target');
        
        if (tabContainer && targetId) {
            // Update tab buttons
            tabContainer.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            tabButton.classList.add('active');
            
            // Update tab content
            const tabContent = document.getElementById(targetId);
            if (tabContent) {
                const allTabContent = document.querySelectorAll('.tab-content');
                allTabContent.forEach(content => {
                    content.classList.remove('active');
                });
                tabContent.classList.add('active');
            }
        }
    },

    // Set active navigation
    setActiveNavigation() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && currentPath.includes(href)) {
                item.classList.add('active');
            }
        });
    },

    // Form handling
    handleFormSubmit(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            const originalText = submitButton.innerHTML;
            const loadingText = submitButton.getAttribute('data-loading-text') || 'Processing...';
            
            submitButton.innerHTML = `<div class="spinner"></div> ${loadingText}`;
            submitButton.disabled = true;
            
            // Re-enable after timeout (fallback)
            setTimeout(() => {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }, 10000);
        }
    },

    // Initialize tooltips
    initializeTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },

    // Show tooltip
    showTooltip(element) {
        const text = element.getAttribute('data-tooltip');
        if (!text) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        tooltip.id = 'active-tooltip';
        
        document.body.appendChild(tooltip);
        
        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    },

    // Hide tooltip
    hideTooltip() {
        const tooltip = document.getElementById('active-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },

    // Initialize form validation
    initializeFormValidation() {
        // Add real-time validation for forms with validation class
        document.querySelectorAll('form.validate').forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('error')) {
                        this.validateField(input);
                    }
                });
            });
        });
    },

    // Validate individual field
    validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        const type = field.type;
        
        // Remove existing error state
        field.classList.remove('error', 'success');
        
        // Check if required field is empty
        if (isRequired && !value) {
            this.setFieldError(field, 'This field is required');
            return false;
        }
        
        // Type-specific validation
        if (value) {
            if (type === 'email' && !this.isValidEmail(value)) {
                this.setFieldError(field, 'Please enter a valid email address');
                return false;
            }
            
            if (field.hasAttribute('minlength')) {
                const minLength = parseInt(field.getAttribute('minlength'));
                if (value.length < minLength) {
                    this.setFieldError(field, `Minimum ${minLength} characters required`);
                    return false;
                }
            }
        }
        
        // Field is valid
        field.classList.add('success');
        this.clearFieldError(field);
        return true;
    },

    // Set field error
    setFieldError(field, message) {
        field.classList.add('error');
        
        let errorElement = field.parentNode.querySelector('.form-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'form-error';
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.innerHTML = `<span>⚠️</span> ${message}`;
    },

    // Clear field error
    clearFieldError(field) {
        const errorElement = field.parentNode.querySelector('.form-error');
        if (errorElement) {
            errorElement.remove();
        }
    },

    // Utility functions
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.HRSystem.init();
    
    // Restore sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        const toggleIcon = document.getElementById('toggle-icon');
        
        if (sidebar && mainContent) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            if (toggleIcon) {
                toggleIcon.textContent = '›';
            }
        }
    }
});

// Handle escape key for modals and dropdowns
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        window.HRSystem.closeModal();
        window.HRSystem.closeAllDropdowns();
        window.HRSystem.closeMobileMenu();
    }
});
