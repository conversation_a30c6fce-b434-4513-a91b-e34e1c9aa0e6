<?php
// Check Existing Database Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Checking Your Existing Database</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .step { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
</style>";

// Database connection
$servername = "localhost";
$username = "root";
$password = "";

echo "<div class='step'>";
echo "<h2>Step 1: Connecting to MySQL</h2>";

$conn = new mysqli($servername, $username, $password);
if ($conn->connect_error) {
    echo "<p class='error'>❌ Connection failed: " . $conn->connect_error . "</p>";
    exit;
} else {
    echo "<p class='success'>✅ MySQL connection successful!</p>";
}
echo "</div>";

echo "<div class='step'>";
echo "<h2>Step 2: Available Databases</h2>";

$result = $conn->query("SHOW DATABASES");
echo "<p class='info'>Found databases:</p>";
echo "<ul>";
while($row = $result->fetch_assoc()) {
    $db_name = $row['Database'];
    if (!in_array($db_name, ['information_schema', 'mysql', 'performance_schema', 'phpmyadmin'])) {
        echo "<li><strong>$db_name</strong></li>";
    }
}
echo "</ul>";
echo "</div>";

// Check for jelodb specifically
echo "<div class='step'>";
echo "<h2>Step 3: Checking jelodb Database</h2>";

$result = $conn->query("SHOW DATABASES LIKE 'jelodb'");
if ($result->num_rows > 0) {
    echo "<p class='success'>✅ jelodb database exists!</p>";
    
    // Connect to jelodb
    $conn->select_db('jelodb');
    
    // Check tables
    echo "<h3>Tables in jelodb:</h3>";
    $result = $conn->query("SHOW TABLES");
    if ($result->num_rows > 0) {
        echo "<ul>";
        while($row = $result->fetch_assoc()) {
            $table_name = $row['Tables_in_jelodb'];
            echo "<li>$table_name</li>";
        }
        echo "</ul>";
        
        // Check users table specifically
        $result = $conn->query("SHOW TABLES LIKE 'users'");
        if ($result->num_rows > 0) {
            echo "<h3>Users Table Structure:</h3>";
            $result = $conn->query("DESCRIBE users");
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check existing users
            echo "<h3>Existing Users:</h3>";
            $result = $conn->query("SELECT id, username, role, created_at FROM users ORDER BY role, username");
            if ($result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Created</th><th>Test Login</th></tr>";
                while($row = $result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $row['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['role']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
                    echo "<td><a href='auth/login.php' target='_blank'>Test</a></td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo "<p class='success'>✅ Found " . $result->num_rows . " users in your database!</p>";
            } else {
                echo "<p class='warning'>⚠️ Users table exists but is empty. Need to add users.</p>";
                
                // Add default users
                echo "<h3>Adding Default Users:</h3>";
                $default_users = [
                    ['admin', 'admin123', 'Admin'],
                    ['hrhead', 'hrhead123', 'HR Head'],
                    ['hrstaff', 'hrstaff123', 'HR Staff'],
                    ['employee1', 'emp123', 'Employee']
                ];
                
                foreach ($default_users as $user) {
                    $stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
                    $hashed_password = md5($user[1]);
                    $stmt->bind_param("sss", $user[0], $hashed_password, $user[2]);
                    
                    if ($stmt->execute()) {
                        echo "<p class='success'>✅ Added user: {$user[0]} ({$user[2]})</p>";
                    } else {
                        echo "<p class='error'>❌ Failed to add user: {$user[0]} - " . $conn->error . "</p>";
                    }
                    $stmt->close();
                }
            }
        } else {
            echo "<p class='error'>❌ Users table not found! Need to create it.</p>";
            
            // Create users table
            $sql = "CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(100) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                role ENUM('Admin','HR Head','HR Staff','Employee') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            if ($conn->query($sql) === TRUE) {
                echo "<p class='success'>✅ Users table created!</p>";
                
                // Add default users
                $default_users = [
                    ['admin', 'admin123', 'Admin'],
                    ['hrhead', 'hrhead123', 'HR Head'],
                    ['hrstaff', 'hrstaff123', 'HR Staff'],
                    ['employee1', 'emp123', 'Employee']
                ];
                
                foreach ($default_users as $user) {
                    $stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
                    $hashed_password = md5($user[1]);
                    $stmt->bind_param("sss", $user[0], $hashed_password, $user[2]);
                    
                    if ($stmt->execute()) {
                        echo "<p class='success'>✅ Added user: {$user[0]} ({$user[2]})</p>";
                    }
                    $stmt->close();
                }
            } else {
                echo "<p class='error'>❌ Failed to create users table: " . $conn->error . "</p>";
            }
        }
    } else {
        echo "<p class='warning'>⚠️ jelodb database exists but has no tables.</p>";
    }
} else {
    echo "<p class='warning'>⚠️ jelodb database not found.</p>";
    
    // Create jelodb database
    if ($conn->query("CREATE DATABASE jelodb") === TRUE) {
        echo "<p class='success'>✅ Created jelodb database!</p>";
        $conn->select_db('jelodb');
        
        // Create users table
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('Admin','HR Head','HR Staff','Employee') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p class='success'>✅ Users table created!</p>";
            
            // Add default users
            $default_users = [
                ['admin', 'admin123', 'Admin'],
                ['hrhead', 'hrhead123', 'HR Head'],
                ['hrstaff', 'hrstaff123', 'HR Staff'],
                ['employee1', 'emp123', 'Employee']
            ];
            
            foreach ($default_users as $user) {
                $stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
                $hashed_password = md5($user[1]);
                $stmt->bind_param("sss", $user[0], $hashed_password, $user[2]);
                
                if ($stmt->execute()) {
                    echo "<p class='success'>✅ Added user: {$user[0]} ({$user[2]})</p>";
                }
                $stmt->close();
            }
        }
    }
}
echo "</div>";

// Test the config file
echo "<div class='step'>";
echo "<h2>Step 4: Testing Config File</h2>";

try {
    include 'config/db.php';
    if (isset($conn) && !$conn->connect_error) {
        echo "<p class='success'>✅ Config file works with your database!</p>";
        
        // Test a simple query
        $result = $conn->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p class='info'>Database has {$row['count']} users.</p>";
        }
    } else {
        echo "<p class='error'>❌ Config file has issues.</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error with config file: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Final instructions
echo "<div class='step'>";
echo "<h2>Step 5: Ready to Login!</h2>";

echo "<h3>🎉 Your database is ready! Use these credentials:</h3>";
echo "<table>";
echo "<tr><th>Role</th><th>Username</th><th>Password</th><th>Action</th></tr>";
echo "<tr><td>Admin</td><td>admin</td><td>admin123</td><td><a href='auth/login.php?u=admin&p=admin123' target='_blank'>Quick Login</a></td></tr>";
echo "<tr><td>HR Head</td><td>hrhead</td><td>hrhead123</td><td><a href='auth/login.php?u=hrhead&p=hrhead123' target='_blank'>Quick Login</a></td></tr>";
echo "<tr><td>HR Staff</td><td>hrstaff</td><td>hrstaff123</td><td><a href='auth/login.php?u=hrstaff&p=hrstaff123' target='_blank'>Quick Login</a></td></tr>";
echo "<tr><td>Employee</td><td>employee1</td><td>emp123</td><td><a href='auth/login.php?u=employee1&p=emp123' target='_blank'>Quick Login</a></td></tr>";
echo "</table>";

echo "<p><strong>Main Login Page:</strong> <a href='auth/login.php' target='_blank' style='font-size: 18px; color: blue;'>Click Here to Login</a></p>";

echo "<h3>If login still doesn't work:</h3>";
echo "<ol>";
echo "<li>Clear your browser cache and cookies</li>";
echo "<li>Make sure the URL is exactly: <code>http://localhost/hr_system_ready/auth/login.php</code></li>";
echo "<li>Try the 'Quick Login' links above</li>";
echo "<li>Check browser console for JavaScript errors (F12)</li>";
echo "</ol>";
echo "</div>";

$conn->close();
?>
