<?php
session_start();
include "../config/db.php";
if ($_SESSION['role'] !== 'HR Staff') { header("Location: ../auth/login.php"); exit; }

// Get HR Staff stats
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$attendance_uploads = $conn->query("SELECT COUNT(*) as count FROM attendance_uploads WHERE uploaded_by={$_SESSION['user_id']}")->fetch_assoc()['count'];
$job_posts = $conn->query("SELECT COUNT(*) as count FROM job_posts WHERE posted_by={$_SESSION['user_id']}")->fetch_assoc()['count'];
$draft_payrolls = $conn->query("SELECT COUNT(*) as count FROM payroll WHERE status='Draft'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>HR Staff Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>Main</h4>
            <a href="hr_staff.php" class="nav-item active">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Attendance</h4>
            <a href="../attendance/upload_attendance.php" class="nav-item">
                <span class="nav-icon">📅</span>
                <span class="nav-text">Upload Attendance</span>
            </a>
            <a href="../reports/attendance_report.php" class="nav-item">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Attendance Reports</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Recruitment</h4>
            <a href="../recruitment/post_job.php" class="nav-item">
                <span class="nav-icon">💼</span>
                <span class="nav-text">Post Job</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Payroll</h4>
            <a href="../payroll/draft_payroll.php" class="nav-item">
                <span class="nav-icon">💰</span>
                <span class="nav-text">Draft Payroll</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Employees</h4>
            <a href="../employees/directory.php" class="nav-item">
                <span class="nav-icon">👥</span>
                <span class="nav-text">Employee Directory</span>
            </a>
            <a href="../leave/my_leaves.php" class="nav-item">
                <span class="nav-icon">📋</span>
                <span class="nav-text">Leave Requests</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Account</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>HR Staff Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">HR Staff</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 HR Operations Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?= $total_employees ?></div>
                    <div class="stat-label">Total Employees</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-number"><?= $attendance_uploads ?></div>
                    <div class="stat-label">Attendance Uploads</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💼</div>
                    <div class="stat-number"><?= $job_posts ?></div>
                    <div class="stat-label">Job Posts Created</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-number"><?= $draft_payrolls ?></div>
                    <div class="stat-label">Draft Payrolls</div>
                </div>
            </div>
        </div>

        <!-- Attendance Management -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📅 Attendance Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📅</div>
                        <div class="card-title">Upload Attendance</div>
                        <div class="card-description">Upload employee attendance records</div>
                        <a href="../attendance/upload_attendance.php" class="card-link">Upload Attendance</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Attendance Reports</div>
                        <div class="card-description">View attendance statistics</div>
                        <a href="../reports/attendance_report.php" class="card-link">View Reports</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recruitment & Payroll -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>💼 Recruitment & Payroll</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">💼</div>
                        <div class="card-title">Post Job</div>
                        <div class="card-description">Create new job postings</div>
                        <a href="../recruitment/post_job.php" class="card-link">Post Job</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💰</div>
                        <div class="card-title">Draft Payroll</div>
                        <div class="card-description">Prepare employee payroll drafts</div>
                        <a href="../payroll/draft_payroll.php" class="card-link">Draft Payroll</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Services -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>👥 Employee Services</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">👥</div>
                        <div class="card-title">Employee Directory</div>
                        <div class="card-description">Browse employee information</div>
                        <a href="../employees/directory.php" class="card-link">View Directory</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📋</div>
                        <div class="card-title">Leave Requests</div>
                        <div class="card-description">Monitor employee leave requests</div>
                        <a href="../leave/my_leaves.php" class="card-link">View Leaves</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📄</div>
                        <div class="card-title">Employee Records</div>
                        <div class="card-description">Manage employee documentation</div>
                        <a href="../employees/records.php" class="card-link">Manage Records</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📞</div>
                        <div class="card-title">Employee Support</div>
                        <div class="card-description">Handle employee inquiries</div>
                        <a href="../support/employee_support.php" class="card-link">Support Center</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
