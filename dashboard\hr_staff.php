<?php
session_start();
include "../config/db.php";
if ($_SESSION['role'] !== 'HR Staff') { header("Location: ../auth/login.php"); exit; }

// Get HR Staff stats
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$attendance_uploads = $conn->query("SELECT COUNT(*) as count FROM attendance_uploads WHERE uploaded_by={$_SESSION['user_id']}")->fetch_assoc()['count'];
$job_posts = $conn->query("SELECT COUNT(*) as count FROM job_posts WHERE posted_by={$_SESSION['user_id']}")->fetch_assoc()['count'];
$draft_payrolls = $conn->query("SELECT COUNT(*) as count FROM payroll WHERE status='Draft'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>HR Staff Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>MAIN</h4>
            <a href="hr_staff.php" class="nav-item active">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>ATTENDANCE</h4>
            <a href="../attendance/upload_attendance.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9,10V12H7V10H9M13,10V12H11V10H13M17,10V12H15V10H17M19,3A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5A2,2 0 0,1 5,3H6V1H8V3H16V1H18V3H19M19,19V8H5V19H19M9,14V16H7V14H9M13,14V16H11V14H13M17,14V16H15V14H17Z"/>
                    </svg>
                </div>
                <span class="nav-text">Upload Attendance</span>
            </a>
            <a href="../reports/attendance_report.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                </div>
                <span class="nav-text">Attendance Reports</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>RECRUITMENT</h4>
            <a href="../recruitment/post_job.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10,2H14A2,2 0 0,1 16,4V6H20A2,2 0 0,1 22,8V19A2,2 0 0,1 20,21H4C2.89,21 2,20.1 2,19V8C2,6.89 2.89,6 4,6H8V4C8,2.89 8.89,2 10,2M14,6V4H10V6H14Z"/>
                    </svg>
                </div>
                <span class="nav-text">Post Job</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>PAYROLL</h4>
            <a href="../payroll/draft_payroll.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                </div>
                <span class="nav-text">Draft Payroll</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>EMPLOYEES</h4>
            <a href="../employees/directory.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v4h2v-7.5c0-.83.67-1.5 1.5-1.5S12 9.67 12 10.5V18h2v-4h3v4h2V9.5c0-1.93-1.57-3.5-3.5-3.5S12 7.57 12 9.5V18H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Employee Directory</span>
            </a>
            <a href="../leave/my_leaves.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V7H5V5H19M5,19V9H19V19H5M6,11H8V13H6V11M6,15H8V17H6V15M10,11H18V13H10V11M10,15H16V17H10V15Z"/>
                    </svg>
                </div>
                <span class="nav-text">Leave Requests</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>ACCOUNT</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                </div>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>HR Staff Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">HR Staff</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 HR Operations Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?= $total_employees ?></div>
                    <div class="stat-label">Total Employees</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-number"><?= $attendance_uploads ?></div>
                    <div class="stat-label">Attendance Uploads</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💼</div>
                    <div class="stat-number"><?= $job_posts ?></div>
                    <div class="stat-label">Job Posts Created</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-number"><?= $draft_payrolls ?></div>
                    <div class="stat-label">Draft Payrolls</div>
                </div>
            </div>
        </div>

        <!-- Attendance Management -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📅 Attendance Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📅</div>
                        <div class="card-title">Upload Attendance</div>
                        <div class="card-description">Upload employee attendance records</div>
                        <a href="../attendance/upload_attendance.php" class="card-link">Upload Attendance</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Attendance Reports</div>
                        <div class="card-description">View attendance statistics</div>
                        <a href="../reports/attendance_report.php" class="card-link">View Reports</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recruitment & Payroll -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>💼 Recruitment & Payroll</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">💼</div>
                        <div class="card-title">Post Job</div>
                        <div class="card-description">Create new job postings</div>
                        <a href="../recruitment/post_job.php" class="card-link">Post Job</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💰</div>
                        <div class="card-title">Draft Payroll</div>
                        <div class="card-description">Prepare employee payroll drafts</div>
                        <a href="../payroll/draft_payroll.php" class="card-link">Draft Payroll</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Services -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>👥 Employee Services</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">👥</div>
                        <div class="card-title">Employee Directory</div>
                        <div class="card-description">Browse employee information</div>
                        <a href="../employees/directory.php" class="card-link">View Directory</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📋</div>
                        <div class="card-title">Leave Requests</div>
                        <div class="card-description">Monitor employee leave requests</div>
                        <a href="../leave/my_leaves.php" class="card-link">View Leaves</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📄</div>
                        <div class="card-title">Employee Records</div>
                        <div class="card-description">Manage employee documentation</div>
                        <a href="../employees/records.php" class="card-link">Manage Records</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📞</div>
                        <div class="card-title">Employee Support</div>
                        <div class="card-description">Handle employee inquiries</div>
                        <a href="../support/employee_support.php" class="card-link">Support Center</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
