<?php
session_start();
include "../config/db.php";
if ($_SESSION['role'] !== 'HR Staff') { header("Location: ../auth/login.php"); exit; }

// Get HR Staff stats
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$attendance_uploads = $conn->query("SELECT COUNT(*) as count FROM attendance_uploads WHERE uploaded_by={$_SESSION['user_id']}")->fetch_assoc()['count'];
$job_posts = $conn->query("SELECT COUNT(*) as count FROM job_posts WHERE posted_by={$_SESSION['user_id']}")->fetch_assoc()['count'];
$draft_payrolls = $conn->query("SELECT COUNT(*) as count FROM payroll WHERE status='Draft'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>HR Staff Dashboard</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>HR Staff Dashboard</header>
<div class="container">
    <div class="welcome-section">
        <h2>Welcome, <?= $_SESSION['username']; ?></h2>
        <p>Manage daily HR operations and employee services</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number"><?= $total_employees ?></div>
            <div class="stat-label">Total Employees</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $attendance_uploads ?></div>
            <div class="stat-label">Attendance Uploads</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $job_posts ?></div>
            <div class="stat-label">Job Posts Created</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $draft_payrolls ?></div>
            <div class="stat-label">Draft Payrolls</div>
        </div>
    </div>

    <div class="quick-actions">
        <h3>HR Operations</h3>
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">📅</div>
                <div class="card-title">Upload Attendance</div>
                <div class="card-description">Upload employee attendance records</div>
                <a href="../attendance/upload_attendance.php" class="card-link">Upload Attendance</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">💼</div>
                <div class="card-title">Post Job</div>
                <div class="card-description">Create new job postings</div>
                <a href="../recruitment/post_job.php" class="card-link">Post Job</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">📋</div>
                <div class="card-title">View Leave Requests</div>
                <div class="card-description">Monitor employee leave requests</div>
                <a href="../leave/my_leaves.php" class="card-link">View Leaves</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">💰</div>
                <div class="card-title">Draft Payroll</div>
                <div class="card-description">Prepare employee payroll drafts</div>
                <a href="../payroll/draft_payroll.php" class="card-link">Draft Payroll</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">📊</div>
                <div class="card-title">Attendance Reports</div>
                <div class="card-description">View attendance statistics</div>
                <a href="../reports/attendance_report.php" class="card-link">View Reports</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">👥</div>
                <div class="card-title">Employee Directory</div>
                <div class="card-description">Browse employee information</div>
                <a href="../employees/directory.php" class="card-link">View Directory</a>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="../auth/logout.php" class="card-link">Logout</a>
    </div>
</div>
</body>
</html>
