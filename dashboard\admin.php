<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }

// Get Admin stats
$total_users = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$total_hr_staff = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='HR Staff'")->fetch_assoc()['count'];
$total_hr_heads = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='HR Head'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>Admin Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>Main</h4>
            <a href="admin.php" class="nav-item active">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>User Management</h4>
            <a href="../auth/register.php" class="nav-item">
                <span class="nav-icon">👤</span>
                <span class="nav-text">Create User</span>
            </a>
            <a href="../admin/manage_users.php" class="nav-item">
                <span class="nav-icon">👥</span>
                <span class="nav-text">Manage Users</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>System</h4>
            <a href="../admin/reports.php" class="nav-item">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Reports</span>
            </a>
            <a href="../admin/settings.php" class="nav-item">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">Settings</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Account</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>Admin Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">Administrator</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 System Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?= $total_users ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👤</div>
                    <div class="stat-number"><?= $total_employees ?></div>
                    <div class="stat-label">Employees</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👔</div>
                    <div class="stat-number"><?= $total_hr_staff ?></div>
                    <div class="stat-label">HR Staff</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👑</div>
                    <div class="stat-number"><?= $total_hr_heads ?></div>
                    <div class="stat-label">HR Heads</div>
                </div>
            </div>
        </div>

        <!-- User Management Section -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>👥 User Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">👤</div>
                        <div class="card-title">Create New User</div>
                        <div class="card-description">Add new employees, HR staff, or admins</div>
                        <a href="../auth/register.php" class="card-link">Create User</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">👥</div>
                        <div class="card-title">Manage Users</div>
                        <div class="card-description">View and manage all system users</div>
                        <a href="../admin/manage_users.php" class="card-link">Manage Users</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Administration Section -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>⚙️ System Administration</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">System Reports</div>
                        <div class="card-description">Generate system-wide reports</div>
                        <a href="../admin/reports.php" class="card-link">View Reports</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">⚙️</div>
                        <div class="card-title">System Settings</div>
                        <div class="card-description">Configure system preferences</div>
                        <a href="../admin/settings.php" class="card-link">Settings</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🔒</div>
                        <div class="card-title">Security</div>
                        <div class="card-description">Manage system security settings</div>
                        <a href="../admin/security.php" class="card-link">Security</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💾</div>
                        <div class="card-title">Backup</div>
                        <div class="card-description">System backup and restore</div>
                        <a href="../admin/backup.php" class="card-link">Backup</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
