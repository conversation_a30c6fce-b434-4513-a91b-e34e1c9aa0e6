<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }

// Get Admin stats
$total_users = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$total_hr_staff = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='HR Staff'")->fetch_assoc()['count'];
$total_hr_heads = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='HR Head'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>Admin Dashboard</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Admin Dashboard</header>
<div class="container">
    <div class="welcome-section">
        <h2>Welcome, <?= $_SESSION['username']; ?></h2>
        <p>System administration and user management</p>
    </div>

    <!-- Stats Overview - Always Visible -->
    <div class="stats-overview">
        <h3>System Overview</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $total_users ?></div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $total_employees ?></div>
                <div class="stat-label">Employees</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $total_hr_staff ?></div>
                <div class="stat-label">HR Staff</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $total_hr_heads ?></div>
                <div class="stat-label">HR Heads</div>
            </div>
        </div>
    </div>

    <!-- User Management Section -->
    <div class="collapsible-section">
        <div class="collapsible-header" onclick="toggleSection(this)">
            <h3>👥 User Management</h3>
            <span class="collapsible-toggle">▼</span>
        </div>
        <div class="collapsible-content">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-icon">👤</div>
                    <div class="card-title">Create New User</div>
                    <div class="card-description">Add new employees, HR staff, or admins</div>
                    <a href="../auth/register.php" class="card-link">Create User</a>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-icon">👥</div>
                    <div class="card-title">Manage Users</div>
                    <div class="card-description">View and manage all system users</div>
                    <a href="../admin/manage_users.php" class="card-link">Manage Users</a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Administration Section -->
    <div class="collapsible-section">
        <div class="collapsible-header" onclick="toggleSection(this)">
            <h3>⚙️ System Administration</h3>
            <span class="collapsible-toggle">▼</span>
        </div>
        <div class="collapsible-content">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-icon">📊</div>
                    <div class="card-title">System Reports</div>
                    <div class="card-description">Generate system-wide reports</div>
                    <a href="../admin/reports.php" class="card-link">View Reports</a>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-icon">⚙️</div>
                    <div class="card-title">System Settings</div>
                    <div class="card-description">Configure system preferences</div>
                    <a href="../admin/settings.php" class="card-link">Settings</a>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="../auth/logout.php" class="card-link">Logout</a>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');
    
    section.classList.toggle('active');
    content.classList.toggle('active');
}
</script>
</body>
</html>
