<?php
session_start();
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }

// Include database connection for stats
include "../config/db.php";

// Get system statistics
$stats = [];

// Total users
$result = $conn->query("SELECT COUNT(*) as count FROM users");
$stats['total_users'] = $result->fetch_assoc()['count'];

// Users by role
$result = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
$stats['users_by_role'] = [];
while($row = $result->fetch_assoc()) {
    $stats['users_by_role'][$row['role']] = $row['count'];
}

// Recent activity (last 7 days)
$stats['recent_logins'] = 0; // Placeholder - would need login tracking table

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - HR System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <?php include "../includes/header.php"; ?>

    <main class="main-content">
        <div class="content-wrapper">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container">
                    <div class="page-header-content">
                        <div>
                            <nav class="breadcrumb">
                                <span class="breadcrumb-item">Admin</span>
                                <span class="breadcrumb-separator">›</span>
                                <span class="breadcrumb-item">Dashboard</span>
                            </nav>
                            <h1 class="page-title">Admin Dashboard</h1>
                            <p class="page-subtitle">Welcome back, <?= htmlspecialchars($_SESSION['username']) ?>! Here's what's happening in your HR system.</p>
                        </div>
                        <div class="page-actions">
                            <a href="../auth/register.php" class="btn btn-primary">
                                <span>👥</span>
                                Create New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container">
                <!-- Statistics Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Total Users</div>
                            <div class="stat-icon">👥</div>
                        </div>
                        <div class="stat-value"><?= $stats['total_users'] ?></div>
                        <div class="stat-change neutral">
                            <span>📊</span>
                            System-wide users
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Administrators</div>
                            <div class="stat-icon">🛡️</div>
                        </div>
                        <div class="stat-value"><?= $stats['users_by_role']['Admin'] ?? 0 ?></div>
                        <div class="stat-change neutral">
                            <span>🔐</span>
                            Admin accounts
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">HR Staff</div>
                            <div class="stat-icon">💼</div>
                        </div>
                        <div class="stat-value"><?= ($stats['users_by_role']['HR Head'] ?? 0) + ($stats['users_by_role']['HR Staff'] ?? 0) ?></div>
                        <div class="stat-change neutral">
                            <span>👔</span>
                            HR personnel
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Employees</div>
                            <div class="stat-icon">👨‍💼</div>
                        </div>
                        <div class="stat-value"><?= $stats['users_by_role']['Employee'] ?? 0 ?></div>
                        <div class="stat-change neutral">
                            <span>🏢</span>
                            Active employees
                        </div>
                    </div>
                </div>

                <!-- Content Grid -->
                <div class="content-grid">
                    <!-- Quick Actions -->
                    <div class="section">
                        <div class="section-header">
                            <h2 class="section-title">Quick Actions</h2>
                            <p class="section-description">Common administrative tasks</p>
                        </div>
                        <div class="section-content">
                            <div class="dashboard-grid">
                                <a href="../auth/register.php" class="quick-action-card">
                                    <div class="quick-action-icon">👥</div>
                                    <div class="quick-action-title">Create User</div>
                                    <div class="quick-action-description">Add new users to the system</div>
                                </a>

                                <a href="#" class="quick-action-card">
                                    <div class="quick-action-icon">⚙️</div>
                                    <div class="quick-action-title">System Settings</div>
                                    <div class="quick-action-description">Configure system preferences</div>
                                </a>

                                <a href="#" class="quick-action-card">
                                    <div class="quick-action-icon">📊</div>
                                    <div class="quick-action-title">Reports</div>
                                    <div class="quick-action-description">Generate system reports</div>
                                </a>

                                <a href="#" class="quick-action-card">
                                    <div class="quick-action-icon">🔒</div>
                                    <div class="quick-action-title">Security</div>
                                    <div class="quick-action-description">Manage security settings</div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- System Overview -->
                    <div class="section">
                        <div class="section-header">
                            <h2 class="section-title">System Overview</h2>
                            <p class="section-description">Current system status</p>
                        </div>
                        <div class="section-content">
                            <div class="alert alert-success">
                                <span>✅</span>
                                <div>
                                    <strong>System Status: Operational</strong><br>
                                    All services are running normally.
                                </div>
                            </div>

                            <div style="margin-top: var(--space-4);">
                                <h4 style="margin-bottom: var(--space-3); color: var(--gray-700);">User Distribution</h4>
                                <?php foreach($stats['users_by_role'] as $role => $count): ?>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--space-2) 0; border-bottom: 1px solid var(--gray-200);">
                                        <span style="color: var(--gray-600);"><?= htmlspecialchars($role) ?></span>
                                        <span class="status-badge neutral"><?= $count ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="section">
                    <div class="section-header">
                        <h2 class="section-title">Recent Activity</h2>
                        <p class="section-description">Latest system activities and user actions</p>
                    </div>
                    <div class="section-content">
                        <div class="alert alert-info">
                            <span>ℹ️</span>
                            <div>
                                <strong>Activity Tracking</strong><br>
                                Activity logging is not yet implemented. This feature will show recent user logins, system changes, and administrative actions.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stat cards on load
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
