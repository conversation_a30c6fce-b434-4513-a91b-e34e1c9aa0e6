<?php
session_start();
include "../config/db.php";
if ($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

// Get employee stats
$user_id = $_SESSION['user_id'];
$pending_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE user_id=$user_id AND status='Pending'")->fetch_assoc()['count'];
$total_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE user_id=$user_id")->fetch_assoc()['count'];
$attendance_count = $conn->query("SELECT COUNT(*) as count FROM attendance_records WHERE employee_id=$user_id AND status='Present'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>Employee Dashboard</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Employee Dashboard</header>
<div class="container">
    <div class="welcome-section">
        <h2>Welcome back, <?= $_SESSION['username']; ?>!</h2>
        <p>Here's your workspace overview</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number"><?= $pending_leaves ?></div>
            <div class="stat-label">Pending Leaves</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $total_leaves ?></div>
            <div class="stat-label">Total Leave Requests</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $attendance_count ?></div>
            <div class="stat-label">Days Present</div>
        </div>
    </div>

    <div class="quick-actions">
        <h3>Quick Actions</h3>
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">📝</div>
                <div class="card-title">Apply for Leave</div>
                <div class="card-description">Submit a new leave request</div>
                <a href="../leave/apply_leave.php" class="card-link">Apply Now</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">📊</div>
                <div class="card-title">Leave Status</div>
                <div class="card-description">View your leave history and status</div>
                <a href="../leave/my_leaves.php" class="card-link">View Leaves</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">⏰</div>
                <div class="card-title">Attendance</div>
                <div class="card-description">Check your attendance records</div>
                <a href="../attendance/my_attendance.php" class="card-link">View Attendance</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">💰</div>
                <div class="card-title">Payslip</div>
                <div class="card-description">Download your salary slips</div>
                <a href="../payroll/view_payslip.php" class="card-link">View Payslip</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">🎯</div>
                <div class="card-title">Performance</div>
                <div class="card-description">View your performance reviews</div>
                <a href="../performance/my_review.php" class="card-link">View Reviews</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">💼</div>
                <div class="card-title">Job Opportunities</div>
                <div class="card-description">Browse available positions</div>
                <a href="../recruitment/job_list.php" class="card-link">View Jobs</a>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="../auth/logout.php" class="card-link">Logout</a>
    </div>
</div>
</body>
</html>
