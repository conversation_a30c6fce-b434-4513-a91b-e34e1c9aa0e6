<?php
session_start();
include "../config/db.php";
if ($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

// Get employee stats
$user_id = $_SESSION['user_id'];
$pending_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE user_id=$user_id AND status='Pending'")->fetch_assoc()['count'];
$total_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE user_id=$user_id")->fetch_assoc()['count'];
$attendance_count = $conn->query("SELECT COUNT(*) as count FROM attendance_records WHERE employee_id=$user_id AND status='Present'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>Employee Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>Main</h4>
            <a href="employee.php" class="nav-item active">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Leave Management</h4>
            <a href="../leave/apply_leave.php" class="nav-item">
                <span class="nav-icon">📝</span>
                <span class="nav-text">Apply Leave</span>
            </a>
            <a href="../leave/my_leaves.php" class="nav-item">
                <span class="nav-icon">📊</span>
                <span class="nav-text">My Leaves</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Attendance</h4>
            <a href="../attendance/my_attendance.php" class="nav-item">
                <span class="nav-icon">⏰</span>
                <span class="nav-text">My Attendance</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Payroll</h4>
            <a href="../payroll/view_payslip.php" class="nav-item">
                <span class="nav-icon">💰</span>
                <span class="nav-text">Payslip</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Performance</h4>
            <a href="../performance/my_review.php" class="nav-item">
                <span class="nav-icon">🎯</span>
                <span class="nav-text">Reviews</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Career</h4>
            <a href="../recruitment/job_list.php" class="nav-item">
                <span class="nav-icon">💼</span>
                <span class="nav-text">Job Opportunities</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Account</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>Employee Dashboard</h1>
            <div class="user-info">
                <span>Welcome back, <?= $_SESSION['username']; ?>!</span>
                <span class="user-role">Employee</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 Your Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number"><?= $pending_leaves ?></div>
                    <div class="stat-label">Pending Leaves</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📝</div>
                    <div class="stat-number"><?= $total_leaves ?></div>
                    <div class="stat-label">Total Leave Requests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number"><?= $attendance_count ?></div>
                    <div class="stat-label">Days Present</div>
                </div>
            </div>
        </div>

        <!-- Leave Management Section -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📝 Leave Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📝</div>
                        <div class="card-title">Apply for Leave</div>
                        <div class="card-description">Submit a new leave request</div>
                        <a href="../leave/apply_leave.php" class="card-link">Apply Now</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Leave Status</div>
                        <div class="card-description">View your leave history and status</div>
                        <a href="../leave/my_leaves.php" class="card-link">View Leaves</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance & Payroll Section -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>⏰ Attendance & Payroll</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">⏰</div>
                        <div class="card-title">Attendance</div>
                        <div class="card-description">Check your attendance records</div>
                        <a href="../attendance/my_attendance.php" class="card-link">View Attendance</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💰</div>
                        <div class="card-title">Payslip</div>
                        <div class="card-description">Download your salary slips</div>
                        <a href="../payroll/view_payslip.php" class="card-link">View Payslip</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance & Career Section -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>🎯 Performance & Career</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Performance</div>
                        <div class="card-description">View your performance reviews</div>
                        <a href="../performance/my_review.php" class="card-link">View Reviews</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💼</div>
                        <div class="card-title">Job Opportunities</div>
                        <div class="card-description">Browse available positions</div>
                        <a href="../recruitment/job_list.php" class="card-link">View Jobs</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📚</div>
                        <div class="card-title">Training</div>
                        <div class="card-description">Access training materials</div>
                        <a href="../training/my_training.php" class="card-link">View Training</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🏆</div>
                        <div class="card-title">Achievements</div>
                        <div class="card-description">View your achievements</div>
                        <a href="../achievements/my_achievements.php" class="card-link">View Achievements</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
