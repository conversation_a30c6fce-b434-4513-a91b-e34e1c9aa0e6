<?php
session_start();
include "../config/db.php";
if ($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

// Get employee stats
$user_id = $_SESSION['user_id'];
$pending_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE user_id=$user_id AND status='Pending'")->fetch_assoc()['count'];
$total_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE user_id=$user_id")->fetch_assoc()['count'];
$attendance_count = $conn->query("SELECT COUNT(*) as count FROM attendance_records WHERE employee_id=$user_id AND status='Present'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>Employee Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>MAIN</h4>
            <a href="employee.php" class="nav-item active">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>LEAVE MANAGEMENT</h4>
            <a href="../leave/apply_leave.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                </div>
                <span class="nav-text">Apply Leave</span>
            </a>
            <a href="../leave/my_leaves.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                </div>
                <span class="nav-text">My Leaves</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>ATTENDANCE</h4>
            <a href="../attendance/my_attendance.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                    </svg>
                </div>
                <span class="nav-text">My Attendance</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>PAYROLL</h4>
            <a href="../payroll/view_payslip.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                </div>
                <span class="nav-text">Payslip</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>PERFORMANCE</h4>
            <a href="../performance/my_review.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
                    </svg>
                </div>
                <span class="nav-text">Reviews</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>CAREER</h4>
            <a href="../recruitment/job_list.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10,2H14A2,2 0 0,1 16,4V6H20A2,2 0 0,1 22,8V19A2,2 0 0,1 20,21H4C2.89,21 2,20.1 2,19V8C2,6.89 2.89,6 4,6H8V4C8,2.89 8.89,2 10,2M14,6V4H10V6H14Z"/>
                    </svg>
                </div>
                <span class="nav-text">Job Opportunities</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>ACCOUNT</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                </div>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>Employee Dashboard</h1>
            <div class="user-info">
                <span>Welcome back, <?= $_SESSION['username']; ?>!</span>
                <span class="user-role">Employee</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 Your Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number"><?= $pending_leaves ?></div>
                    <div class="stat-label">Pending Leaves</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📝</div>
                    <div class="stat-number"><?= $total_leaves ?></div>
                    <div class="stat-label">Total Leave Requests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number"><?= $attendance_count ?></div>
                    <div class="stat-label">Days Present</div>
                </div>
            </div>
        </div>

        <!-- Leave Management Section -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📝 Leave Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📝</div>
                        <div class="card-title">Apply for Leave</div>
                        <div class="card-description">Submit a new leave request</div>
                        <a href="../leave/apply_leave.php" class="card-link">Apply Now</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Leave Status</div>
                        <div class="card-description">View your leave history and status</div>
                        <a href="../leave/my_leaves.php" class="card-link">View Leaves</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance & Payroll Section -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>⏰ Attendance & Payroll</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">⏰</div>
                        <div class="card-title">Attendance</div>
                        <div class="card-description">Check your attendance records</div>
                        <a href="../attendance/my_attendance.php" class="card-link">View Attendance</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💰</div>
                        <div class="card-title">Payslip</div>
                        <div class="card-description">Download your salary slips</div>
                        <a href="../payroll/view_payslip.php" class="card-link">View Payslip</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance & Career Section -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>🎯 Performance & Career</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Performance</div>
                        <div class="card-description">View your performance reviews</div>
                        <a href="../performance/my_review.php" class="card-link">View Reviews</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">💼</div>
                        <div class="card-title">Job Opportunities</div>
                        <div class="card-description">Browse available positions</div>
                        <a href="../recruitment/job_list.php" class="card-link">View Jobs</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📚</div>
                        <div class="card-title">Training</div>
                        <div class="card-description">Access training materials</div>
                        <a href="../training/my_training.php" class="card-link">View Training</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🏆</div>
                        <div class="card-title">Achievements</div>
                        <div class="card-description">View your achievements</div>
                        <a href="../achievements/my_achievements.php" class="card-link">View Achievements</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
