<?php
session_start();
if ($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

// Include database connection for employee stats
include "../config/db.php";

// Get employee statistics (placeholder data)
$employee_stats = [
    'pending_leaves' => 2,
    'approved_leaves' => 8,
    'attendance_rate' => 95,
    'pending_reviews' => 1
];

// Get current date info
$current_date = date('Y-m-d');
$current_month = date('F Y');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Dashboard - HR System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <?php include "../includes/header.php"; ?>

    <main class="main-content">
        <div class="content-wrapper">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container">
                    <div class="page-header-content">
                        <div>
                            <nav class="breadcrumb">
                                <span class="breadcrumb-item">Employee</span>
                                <span class="breadcrumb-separator">›</span>
                                <span class="breadcrumb-item">Dashboard</span>
                            </nav>
                            <h1 class="page-title">Employee Dashboard</h1>
                            <p class="page-subtitle">Welcome back, <?= htmlspecialchars($_SESSION['username']) ?>! Here's your personal overview for <?= $current_month ?>.</p>
                        </div>
                        <div class="page-actions">
                            <a href="../leave/apply_leave.php" class="btn btn-primary">
                                <span>📅</span>
                                Apply Leave
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container">
                <!-- Statistics Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Pending Leaves</div>
                            <div class="stat-icon">⏳</div>
                        </div>
                        <div class="stat-value"><?= $employee_stats['pending_leaves'] ?></div>
                        <div class="stat-change neutral">
                            <span>📋</span>
                            Awaiting approval
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Approved Leaves</div>
                            <div class="stat-icon">✅</div>
                        </div>
                        <div class="stat-value"><?= $employee_stats['approved_leaves'] ?></div>
                        <div class="stat-change positive">
                            <span>📈</span>
                            This year
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Attendance Rate</div>
                            <div class="stat-icon">⏰</div>
                        </div>
                        <div class="stat-value"><?= $employee_stats['attendance_rate'] ?>%</div>
                        <div class="stat-change positive">
                            <span>📊</span>
                            This month
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Pending Reviews</div>
                            <div class="stat-icon">⭐</div>
                        </div>
                        <div class="stat-value"><?= $employee_stats['pending_reviews'] ?></div>
                        <div class="stat-change neutral">
                            <span>📝</span>
                            Performance reviews
                        </div>
                    </div>
                </div>

                <!-- Content Grid -->
                <div class="content-grid">
                    <!-- Quick Actions -->
                    <div class="section">
                        <div class="section-header">
                            <h2 class="section-title">Quick Actions</h2>
                            <p class="section-description">Common employee tasks</p>
                        </div>
                        <div class="section-content">
                            <div class="dashboard-grid">
                                <a href="../leave/apply_leave.php" class="quick-action-card">
                                    <div class="quick-action-icon">📅</div>
                                    <div class="quick-action-title">Apply Leave</div>
                                    <div class="quick-action-description">Request time off</div>
                                </a>

                                <a href="../leave/my_leaves.php" class="quick-action-card">
                                    <div class="quick-action-icon">📋</div>
                                    <div class="quick-action-title">My Leaves</div>
                                    <div class="quick-action-description">View leave status</div>
                                </a>

                                <a href="../attendance/my_attendance.php" class="quick-action-card">
                                    <div class="quick-action-icon">⏰</div>
                                    <div class="quick-action-title">Attendance</div>
                                    <div class="quick-action-description">Check attendance records</div>
                                </a>

                                <a href="../payroll/view_payslip.php" class="quick-action-card">
                                    <div class="quick-action-icon">💰</div>
                                    <div class="quick-action-title">Payslip</div>
                                    <div class="quick-action-description">View salary details</div>
                                </a>

                                <a href="../recruitment/job_list.php" class="quick-action-card">
                                    <div class="quick-action-icon">💼</div>
                                    <div class="quick-action-title">Job Openings</div>
                                    <div class="quick-action-description">Internal opportunities</div>
                                </a>

                                <a href="../performance/my_review.php" class="quick-action-card">
                                    <div class="quick-action-icon">⭐</div>
                                    <div class="quick-action-title">Performance</div>
                                    <div class="quick-action-description">View reviews</div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="section">
                        <div class="section-header">
                            <h2 class="section-title">Recent Activity</h2>
                            <p class="section-description">Your latest actions</p>
                        </div>
                        <div class="section-content">
                            <div class="alert alert-info">
                                <span>ℹ️</span>
                                <div>
                                    <strong>Activity Tracking</strong><br>
                                    Your recent activities will appear here once the activity logging system is implemented.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Events -->
                <div class="section">
                    <div class="section-header">
                        <h2 class="section-title">Upcoming Events & Reminders</h2>
                        <p class="section-description">Important dates and deadlines</p>
                    </div>
                    <div class="section-content">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Performance Review</td>
                                        <td>2024-01-15</td>
                                        <td>Review</td>
                                        <td><span class="status-badge warning">Pending</span></td>
                                    </tr>
                                    <tr>
                                        <td>Team Meeting</td>
                                        <td>2024-01-10</td>
                                        <td>Meeting</td>
                                        <td><span class="status-badge info">Scheduled</span></td>
                                    </tr>
                                    <tr>
                                        <td>Training Session</td>
                                        <td>2024-01-20</td>
                                        <td>Training</td>
                                        <td><span class="status-badge success">Enrolled</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stat cards on load
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // Add click animation to quick action cards
            const quickActions = document.querySelectorAll('.quick-action-card');
            quickActions.forEach(card => {
                card.addEventListener('click', function(e) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
