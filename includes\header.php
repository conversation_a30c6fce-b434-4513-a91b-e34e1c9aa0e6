<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get user initials for avatar
function getUserInitials($username) {
    $names = explode(' ', $username);
    $initials = '';
    foreach ($names as $name) {
        $initials .= strtoupper(substr($name, 0, 1));
    }
    return substr($initials, 0, 2);
}

$userInitials = isset($_SESSION['username']) ? getUserInitials($_SESSION['username']) : 'U';
$userRole = isset($_SESSION['role']) ? $_SESSION['role'] : 'Guest';
$userName = isset($_SESSION['username']) ? $_SESSION['username'] : 'Guest';
?>

<!-- Mobile Menu Toggle -->
<button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
    <span>☰</span>
</button>

<!-- Mobile Overlay -->
<div class="mobile-overlay" onclick="closeMobileMenu()"></div>

<!-- Modern Sidebar -->
<div class="sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-logo">HR</div>
        <div class="sidebar-title">HR System</div>
    </div>

    <!-- Sidebar Navigation -->
    <nav class="sidebar-nav">
        <?php if (isset($_SESSION['role'])): ?>
            
            <?php if ($_SESSION['role'] === 'Admin'): ?>
                <div class="nav-section">
                    <div class="nav-section-title">Administration</div>
                    <a href="../dashboard/admin.php" class="nav-item">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="../auth/register.php" class="nav-item">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">Create User</span>
                    </a>
                </div>
            <?php endif; ?>

            <?php if ($_SESSION['role'] === 'HR Head' || $_SESSION['role'] === 'HR Staff'): ?>
                <div class="nav-section">
                    <div class="nav-section-title">HR Management</div>
                    <a href="../dashboard/<?= strtolower(str_replace(' ', '_', $_SESSION['role'])) ?>.php" class="nav-item">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="../leave/manage_leaves.php" class="nav-item">
                        <span class="nav-icon">📅</span>
                        <span class="nav-text">Manage Leaves</span>
                    </a>
                    <a href="../recruitment/post_job.php" class="nav-item">
                        <span class="nav-icon">💼</span>
                        <span class="nav-text">Post Jobs</span>
                    </a>
                    <a href="../recruitment/applications.php" class="nav-item">
                        <span class="nav-icon">📄</span>
                        <span class="nav-text">Applications</span>
                    </a>
                    <a href="../payroll/draft_payroll.php" class="nav-item">
                        <span class="nav-icon">💰</span>
                        <span class="nav-text">Payroll</span>
                    </a>
                    <a href="../performance/add_review.php" class="nav-item">
                        <span class="nav-icon">⭐</span>
                        <span class="nav-text">Performance</span>
                    </a>
                    <a href="../attendance/upload_attendance.php" class="nav-item">
                        <span class="nav-icon">⏰</span>
                        <span class="nav-text">Attendance</span>
                    </a>
                </div>
            <?php endif; ?>

            <?php if ($_SESSION['role'] === 'Employee'): ?>
                <div class="nav-section">
                    <div class="nav-section-title">Employee Portal</div>
                    <a href="../dashboard/employee.php" class="nav-item">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="../leave/apply_leave.php" class="nav-item">
                        <span class="nav-icon">📅</span>
                        <span class="nav-text">Apply Leave</span>
                    </a>
                    <a href="../leave/my_leaves.php" class="nav-item">
                        <span class="nav-icon">📋</span>
                        <span class="nav-text">My Leaves</span>
                    </a>
                    <a href="../attendance/my_attendance.php" class="nav-item">
                        <span class="nav-icon">⏰</span>
                        <span class="nav-text">My Attendance</span>
                    </a>
                    <a href="../payroll/view_payslip.php" class="nav-item">
                        <span class="nav-icon">💰</span>
                        <span class="nav-text">Payslip</span>
                    </a>
                    <a href="../recruitment/job_list.php" class="nav-item">
                        <span class="nav-icon">💼</span>
                        <span class="nav-text">Job Openings</span>
                    </a>
                    <a href="../performance/my_review.php" class="nav-item">
                        <span class="nav-icon">⭐</span>
                        <span class="nav-text">My Reviews</span>
                    </a>
                </div>
            <?php endif; ?>

        <?php endif; ?>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <?php if (isset($_SESSION['username'])): ?>
            <div class="user-profile">
                <div class="user-avatar"><?= $userInitials ?></div>
                <div class="user-info">
                    <div class="user-name"><?= htmlspecialchars($userName) ?></div>
                    <div class="user-role"><?= htmlspecialchars($userRole) ?></div>
                </div>
            </div>
            <a href="../auth/logout.php" class="nav-item" style="margin-top: 10px;">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">Logout</span>
            </a>
        <?php endif; ?>
    </div>

    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <span id="toggle-icon">‹</span>
    </button>
</div>

<!-- Include main JavaScript file -->
<script src="../assets/app.js"></script>

<script>
// Legacy function support for backward compatibility
function toggleSidebar() {
    window.HRSystem.toggleSidebar();
}

function toggleMobileMenu() {
    window.HRSystem.toggleMobileMenu();
}

function closeMobileMenu() {
    window.HRSystem.closeMobileMenu();
}
</script>
