/* ==========================
   MODERN CSS VARIABLES
========================== */
:root {
    /* Primary Colors */
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-800: #3730a3;
    --primary-50: #eef2ff;
    --primary-100: #e0e7ff;

    /* Secondary Colors */
    --secondary-500: #06b6d4;
    --secondary-600: #0891b2;
    --secondary-50: #ecfeff;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Status Colors */
    --success-500: #10b981;
    --success-50: #ecfdf5;
    --warning-500: #f59e0b;
    --warning-50: #fffbeb;
    --error-500: #ef4444;
    --error-50: #fef2f2;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Typography */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

/* ==========================
   GLOBAL RESET & BASE STYLES
========================== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    margin: 0;
    padding: 0;
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

h1, h2, h3, h4, h5, h6 {
    margin: 0 0 var(--space-4) 0;
    font-weight: 600;
    line-height: 1.3;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin: 0 0 var(--space-4) 0;
}

img {
    max-width: 100%;
    height: auto;
}

/* ==========================
   UTILITY CLASSES
========================== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.gap-4 {
    gap: var(--space-4);
}

.gap-6 {
    gap: var(--space-6);
}

.text-center {
    text-align: center;
}

.text-sm {
    font-size: 0.875rem;
}

.text-lg {
    font-size: 1.125rem;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.text-gray-500 {
    color: var(--gray-500);
}

.text-gray-600 {
    color: var(--gray-600);
}

.text-primary {
    color: var(--primary-600);
}

.bg-white {
    background-color: white;
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.mb-4 {
    margin-bottom: var(--space-4);
}

.mb-6 {
    margin-bottom: var(--space-6);
}

.mt-4 {
    margin-top: var(--space-4);
}

.p-4 {
    padding: var(--space-4);
}

.p-6 {
    padding: var(--space-6);
}

.px-4 {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

.py-2 {
    padding-top: var(--space-2);
    padding-bottom: var(--space-2);
}

.py-3 {
    padding-top: var(--space-3);
    padding-bottom: var(--space-3);
}

.w-full {
    width: 100%;
}

.hidden {
    display: none;
}

@media (max-width: 768px) {
    .md\:hidden {
        display: none;
    }

    .md\:block {
        display: block;
    }
}

/* ==========================
   MODERN SIDEBAR
========================== */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(180deg, var(--primary-600) 0%, var(--primary-800) 100%);
    color: white;
    overflow-y: auto;
    overflow-x: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    box-shadow: var(--shadow-xl);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: var(--space-6) var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    background: var(--primary-50);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--primary-600);
    font-size: 1.25rem;
}

.sidebar-title {
    font-size: 1.25rem;
    font-weight: 600;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-title {
    opacity: 0;
}

.sidebar-nav {
    padding: var(--space-4) 0;
}

.nav-section {
    margin-bottom: var(--space-6);
}

.nav-section-title {
    padding: 0 var(--space-4);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: var(--space-2);
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
}

.nav-item {
    display: block;
    padding: var(--space-3) var(--space-4);
    color: rgba(255, 255, 255, 0.9);
    margin: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    font-weight: 500;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--primary-50);
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.nav-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
}

.sidebar-toggle {
    position: absolute;
    top: var(--space-4);
    right: -12px;
    width: 24px;
    height: 24px;
    background: white;
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    z-index: 1001;
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--space-4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.user-profile:hover {
    background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--primary-50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--primary-600);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.user-info {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .user-info {
    opacity: 0;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 2px;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

/* ==========================
   MAIN CONTENT LAYOUT
========================== */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    background: var(--gray-50);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-content.expanded {
    margin-left: 80px;
}

.content-wrapper {
    padding: var(--space-6);
    max-width: 1400px;
    margin: 0 auto;
}

/* ==========================
   MODERN HEADER
========================== */
.page-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6) 0;
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-sm);
}

.page-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.page-subtitle {
    color: var(--gray-600);
    margin: var(--space-2) 0 0 0;
    font-size: 1rem;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-bottom: var(--space-2);
}

.breadcrumb-item {
    color: var(--gray-500);
}

.breadcrumb-item:last-child {
    color: var(--gray-900);
    font-weight: 500;
}

.breadcrumb-separator {
    color: var(--gray-300);
}

/* ==========================
   DASHBOARD GRID
========================== */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
}

/* ==========================
   SECTION CONTAINERS
========================== */
.section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin-bottom: var(--space-6);
}

.section-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.section-description {
    color: var(--gray-600);
    margin: var(--space-1) 0 0 0;
    font-size: 0.875rem;
}

.section-content {
    padding: var(--space-6);
}

.section-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ==========================
   MODERN CARDS
========================== */
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.card-subtitle {
    color: var(--gray-600);
    margin: var(--space-1) 0 0 0;
    font-size: 0.875rem;
}

.card-content {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-600), var(--secondary-500));
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    background: var(--primary-50);
    color: var(--primary-600);
}

.stat-value {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: 0.875rem;
}

.stat-change.positive {
    color: var(--success-500);
}

.stat-change.negative {
    color: var(--error-500);
}

.stat-change.neutral {
    color: var(--gray-500);
}

/* Quick Action Cards */
.quick-action-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.quick-action-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--primary-300);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: var(--primary-50);
    color: var(--primary-600);
    margin: 0 auto var(--space-3) auto;
}

.quick-action-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.quick-action-description {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* ==========================
   MODERN TABLES
========================== */
.table-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.table th {
    background: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    text-align: left;
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-900);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Table Variants */
.table-striped tbody tr:nth-child(even) {
    background: var(--gray-50);
}

.table-striped tbody tr:nth-child(even):hover {
    background: var(--gray-100);
}

/* Table Actions */
.table-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.table-action {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.table-action.edit {
    background: var(--primary-50);
    color: var(--primary-600);
}

.table-action.edit:hover {
    background: var(--primary-100);
}

.table-action.delete {
    background: var(--error-50);
    color: var(--error-600);
}

.table-action.delete:hover {
    background: var(--error-100);
}

.table-action.view {
    background: var(--gray-100);
    color: var(--gray-600);
}

.table-action.view:hover {
    background: var(--gray-200);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background: var(--success-50);
    color: var(--success-700);
}

.status-badge.warning {
    background: var(--warning-50);
    color: var(--warning-700);
}

.status-badge.error {
    background: var(--error-50);
    color: var(--error-700);
}

.status-badge.info {
    background: var(--primary-50);
    color: var(--primary-700);
}

.status-badge.neutral {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* Responsive Table */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }

    .table {
        min-width: 600px;
    }
}

/* Legacy table support */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--space-5);
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

table th, table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    text-align: left;
}

table th {
    background: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    font-size: 0.875rem;
}

table tr:nth-child(even) {
    background: var(--gray-50);
}

table tr:hover {
    background: var(--gray-100);
}

/* ==========================
   MODERN BUTTONS
========================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
}

.btn-xl {
    padding: var(--space-5) var(--space-8);
    font-size: 1.125rem;
}

/* Button Variants */
.btn-primary {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
}

.btn-primary:hover {
    background: var(--primary-700);
    border-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-500);
    color: white;
    border-color: var(--secondary-500);
}

.btn-secondary:hover {
    background: var(--secondary-600);
    border-color: var(--secondary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-outline:hover {
    background: var(--primary-600);
    color: white;
}

.btn-ghost {
    background: transparent;
    color: var(--gray-600);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.btn-success {
    background: var(--success-500);
    color: white;
    border-color: var(--success-500);
}

.btn-success:hover {
    background: var(--success-600);
    border-color: var(--success-600);
}

.btn-warning {
    background: var(--warning-500);
    color: white;
    border-color: var(--warning-500);
}

.btn-warning:hover {
    background: var(--warning-600);
    border-color: var(--warning-600);
}

.btn-danger {
    background: var(--error-500);
    color: white;
    border-color: var(--error-500);
}

.btn-danger:hover {
    background: var(--error-600);
    border-color: var(--error-600);
}

/* Button Groups */
.btn-group {
    display: inline-flex;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
    border-right-width: 1px;
}

/* Legacy button support */
button {
    background-color: var(--primary-600);
    color: white;
    border: none;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

button:hover {
    background-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ==========================
   MODERN FORMS
========================== */
.form-group {
    margin-bottom: var(--space-5);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.form-label.required::after {
    content: ' *';
    color: var(--error-500);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--gray-900);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input:disabled {
    background: var(--gray-50);
    color: var(--gray-500);
    cursor: not-allowed;
}

.form-input.error {
    border-color: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

.form-help {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: var(--space-1);
}

.form-error {
    font-size: 0.75rem;
    color: var(--error-500);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.form-success {
    font-size: 0.75rem;
    color: var(--success-500);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* Form Layout */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.form-row.three-cols {
    grid-template-columns: 1fr 1fr 1fr;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* Checkbox and Radio */
.form-checkbox,
.form-radio {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.form-checkbox input,
.form-radio input {
    width: auto;
    margin: 0;
}

/* File Upload */
.form-file {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.form-file input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.form-file-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--gray-50);
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.form-file:hover .form-file-label {
    border-color: var(--primary-400);
    background: var(--primary-50);
    color: var(--primary-600);
}

/* Legacy form support */
input, select, textarea {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    margin: var(--space-2) 0 var(--space-4) 0;
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-300);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* ==========================
   MODERN COMPONENTS
========================== */

/* Loading Spinner */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alerts */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-4);
    border: 1px solid;
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert-success {
    background: var(--success-50);
    border-color: var(--success-200);
    color: var(--success-800);
}

.alert-warning {
    background: var(--warning-50);
    border-color: var(--warning-200);
    color: var(--warning-800);
}

.alert-error {
    background: var(--error-50);
    border-color: var(--error-200);
    color: var(--error-800);
}

.alert-info {
    background: var(--primary-50);
    border-color: var(--primary-200);
    color: var(--primary-800);
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--gray-600);
}

.modal-content {
    padding: var(--space-6);
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* Tabs */
.tabs {
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: var(--space-6);
}

.tab-list {
    display: flex;
    gap: var(--space-1);
}

.tab-button {
    padding: var(--space-3) var(--space-4);
    border: none;
    background: none;
    color: var(--gray-600);
    font-weight: 500;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: var(--gray-900);
}

.tab-button.active {
    color: var(--primary-600);
    border-bottom-color: var(--primary-600);
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    border-color: var(--gray-400);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--space-3) var(--space-4);
    color: var(--gray-700);
    text-decoration: none;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

/* ==========================
   RESPONSIVE DESIGN
========================== */
@media (max-width: 1024px) {
    .sidebar {
        width: 80px;
    }

    .sidebar .nav-text,
    .sidebar .sidebar-title,
    .sidebar .nav-section-title,
    .sidebar .user-info {
        display: none;
    }

    .main-content {
        margin-left: 80px;
    }

    .content-wrapper {
        padding: var(--space-4);
    }

    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
        z-index: 9999;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .dashboard-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: var(--radius-md);
        border-right-width: 1px;
        border-bottom-width: 0;
    }

    .btn-group .btn:not(:last-child) {
        border-bottom-width: 0;
    }

    .btn-group .btn:last-child {
        border-bottom-width: 1px;
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        padding: var(--space-3);
    }

    .card-content,
    .section-content {
        padding: var(--space-4);
    }

    .table th,
    .table td {
        padding: var(--space-3) var(--space-4);
    }

    .modal {
        width: 95%;
        margin: var(--space-4);
    }

    .modal-header,
    .modal-content,
    .modal-footer {
        padding: var(--space-4);
    }
}

/* Mobile Navigation Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: var(--space-4);
    left: var(--space-4);
    z-index: 10000;
    background: var(--primary-600);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-3);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* ==========================
   TOOLTIPS
========================== */
.tooltip {
    position: absolute;
    background: var(--gray-900);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    z-index: 10000;
    pointer-events: none;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease forwards;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--gray-900);
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================
   ANIMATIONS
========================== */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Pulse animation for loading states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

/* Slide in from left */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease;
}

/* Scale in animation */
@keyframes scaleIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.scale-in {
    animation: scaleIn 0.3s ease;
}

/* ==========================
   ACCESSIBILITY
========================== */
/* Focus styles */
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Skip to content link */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-600);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: 10000;
    transition: top 0.3s ease;
}

.skip-to-content:focus {
    top: 6px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --gray-50: #ffffff;
        --gray-100: #f0f0f0;
        --gray-900: #000000;
        --primary-600: #0000ff;
        --primary-700: #0000cc;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .mobile-menu-toggle,
    .page-actions,
    .table-actions,
    .btn,
    .alert {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .page-header {
        border-bottom: 2px solid #000;
        box-shadow: none;
    }

    .card,
    .section {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }

    .page-title {
        font-size: 18px;
    }

    body {
        font-size: 12px;
        line-height: 1.4;
    }
}

