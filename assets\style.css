body {
    font-family: Arial, sans-serif;
    background-color: #f0f5fb;
    color: #1a1a1a;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1e3a8a;
    color: #fff;
    padding: 15px;
    text-align: center;
}

nav a, a {
    color: #1e3a8a;
    text-decoration: none;
}

.container {
    max-width: 800px;
    margin: 20px auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(30, 58, 138, 0.2);
}

h2 { color: #1e3a8a; }

input, select, textarea, button {
    width: 100%;
    padding: 10px;
    margin: 5px 0 15px;
    border: 1px solid #1e3a8a;
    border-radius: 5px;
}

button {
    background-color: #1e3a8a;
    color: white;
    border: none;
    cursor: pointer;
    transition: 0.3s;
}

button:hover { background-color: #3b82f6; }

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table, th, td { border: 1px solid #1e3a8a; }
th, td { padding: 10px; text-align: left; }
th { background-color: #3b82f6; color: white; }

header .logo {
    height: 60px;  /* adjust as needed */
    width: auto;
}

/* Dashboard improvements */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.dashboard-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: #1e3a8a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    color: white;
    font-size: 24px;
}

.card-title {
    font-size: 18px;
    font-weight: bold;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.card-description {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 15px;
}

.card-link {
    display: inline-block;
    background: #1e3a8a;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 14px;
    transition: background 0.2s;
}

.card-link:hover {
    background: #3b82f6;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}

.welcome-section {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h2 {
    color: white;
    margin: 0;
    font-size: 28px;
}

.quick-actions {
    margin-top: 30px;
}

.quick-actions h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
}

/* Collapsible sections */
.collapsible-section {
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    background: white;
}

.collapsible-header {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s;
}

.collapsible-header:hover {
    background: linear-gradient(135deg, #3b82f6, #1e3a8a);
}

.collapsible-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
}

.collapsible-toggle {
    font-size: 20px;
    transition: transform 0.3s;
}

.collapsible-content {
    padding: 20px;
    display: none;
}

.collapsible-content.active {
    display: block;
}

.collapsible-section.active .collapsible-toggle {
    transform: rotate(180deg);
}

/* Stats overview always visible */
.stats-overview {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.stats-overview h3 {
    color: white;
    margin-top: 0;
    text-align: center;
}
