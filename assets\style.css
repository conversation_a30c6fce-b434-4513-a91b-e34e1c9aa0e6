body {
    font-family: Arial, sans-serif;
    background-color: #f0f5fb;
    color: #1a1a1a;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1e3a8a;
    color: #fff;
    padding: 15px;
    text-align: center;
}

nav a, a {
    color: #1e3a8a;
    text-decoration: none;
}

.container {
    max-width: 800px;
    margin: 20px auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(30, 58, 138, 0.2);
}

h2 { color: #1e3a8a; }

input, select, textarea, button {
    width: 100%;
    padding: 10px;
    margin: 5px 0 15px;
    border: 1px solid #1e3a8a;
    border-radius: 5px;
}

button {
    background-color: #1e3a8a;
    color: white;
    border: none;
    cursor: pointer;
    transition: 0.3s;
}

button:hover { background-color: #3b82f6; }

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table, th, td { border: 1px solid #1e3a8a; }
th, td { padding: 10px; text-align: left; }
th { background-color: #3b82f6; color: white; }

header .logo {
    height: 60px;  /* adjust as needed */
    width: auto;
}

/* Dashboard improvements */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.dashboard-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: #1e3a8a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    color: white;
    font-size: 24px;
}

.card-title {
    font-size: 18px;
    font-weight: bold;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.card-description {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 15px;
}

.card-link {
    display: inline-block;
    background: #1e3a8a;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 14px;
    transition: background 0.2s;
}

.card-link:hover {
    background: #3b82f6;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}

.welcome-section {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h2 {
    color: white;
    margin: 0;
    font-size: 28px;
}

.quick-actions {
    margin-top: 30px;
}

.quick-actions h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
}

/* Collapsible sections */
.collapsible-section {
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    background: white;
}

.collapsible-header {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s;
}

.collapsible-header:hover {
    background: linear-gradient(135deg, #3b82f6, #1e3a8a);
}

.collapsible-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
}

.collapsible-toggle {
    font-size: 20px;
    transition: transform 0.3s;
}

.collapsible-content {
    padding: 20px;
    display: none;
}

.collapsible-content.active {
    display: block;
}

.collapsible-section.active .collapsible-toggle {
    transform: rotate(180deg);
}

/* Stats overview always visible */
.stats-overview {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.stats-overview h3 {
    color: white;
    margin-top: 0;
    text-align: center;
}

/* Sidebar Navigation Styles */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    transition: transform 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.collapsed {
    transform: translateX(-280px);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    height: 40px;
    width: auto;
    filter: brightness(0) invert(1);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.2s;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section h4 {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0 20px 15px;
    font-weight: 600;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: white;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: white;
}

.nav-item.logout:hover {
    background: rgba(239, 68, 68, 0.2);
    border-left-color: #ef4444;
}

.nav-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

/* Main Content Layout */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    background: #f8fafc;
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 0;
}

.page-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: #1e3a8a;
    margin: 0;
    font-size: 28px;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.user-info span:first-child {
    font-weight: 600;
    color: #374151;
}

.user-role {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.content-wrapper {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Enhanced Stats Cards */
.stat-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-280px);
    }

    .sidebar.collapsed {
        transform: translateX(-280px);
    }

    .main-content {
        margin-left: 0;
    }

    .content-wrapper {
        padding: 20px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .user-info {
        align-items: flex-start;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
