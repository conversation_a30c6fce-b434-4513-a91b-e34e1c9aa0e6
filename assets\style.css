body {
    font-family: Arial, sans-serif;
    background-color: #f0f5fb;
    color: #1a1a1a;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1e3a8a;
    color: #fff;
    padding: 15px;
    text-align: center;
}

nav a, a {
    color: #1e3a8a;
    text-decoration: none;
}

.container {
    max-width: 800px;
    margin: 20px auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(30, 58, 138, 0.2);
}

h2 { color: #1e3a8a; }

input, select, textarea, button {
    width: 100%;
    padding: 10px;
    margin: 5px 0 15px;
    border: 1px solid #1e3a8a;
    border-radius: 5px;
}

button {
    background-color: #1e3a8a;
    color: white;
    border: none;
    cursor: pointer;
    transition: 0.3s;
}

button:hover { background-color: #3b82f6; }

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table, th, td { border: 1px solid #1e3a8a; }
th, td { padding: 10px; text-align: left; }
th { background-color: #3b82f6; color: white; }

header .logo {
    height: 60px;  /* adjust as needed */
    width: auto;
}
