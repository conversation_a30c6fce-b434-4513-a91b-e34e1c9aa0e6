body {
    font-family: Arial, sans-serif;
    background-color: #f0f5fb;
    color: #1a1a1a;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1e3a8a;
    color: #fff;
    padding: 15px;
    text-align: center;
}

nav a, a {
    color: #1e3a8a;
    text-decoration: none;
}

.container {
    max-width: 800px;
    margin: 20px auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(30, 58, 138, 0.2);
}

h2 { color: #1e3a8a; }

input, select, textarea, button {
    width: 100%;
    padding: 10px;
    margin: 5px 0 15px;
    border: 1px solid #1e3a8a;
    border-radius: 5px;
}

button {
    background-color: #1e3a8a;
    color: white;
    border: none;
    cursor: pointer;
    transition: 0.3s;
}

button:hover { background-color: #3b82f6; }

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table, th, td { border: 1px solid #1e3a8a; }
th, td { padding: 10px; text-align: left; }
th { background-color: #3b82f6; color: white; }

header .logo {
    height: 60px;  /* adjust as needed */
    width: auto;
}

/* Dashboard improvements */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.dashboard-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2563eb, #1d4ed8);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
    border-color: #cbd5e1;
}

.dashboard-card:hover::before {
    opacity: 1;
}

.card-icon {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 18px;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 8px;
    letter-spacing: -0.3px;
}

.card-description {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 18px;
    line-height: 1.5;
}

.card-link {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    padding: 10px 18px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.card-link:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    padding: 24px;
    border-radius: 16px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25);
    transition: all 0.3s ease;
}

.stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.35);
}

.stat-icon {
    font-size: 28px;
    margin-bottom: 12px;
    opacity: 0.9;
}

.stat-number {
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 8px;
    letter-spacing: -1px;
    position: relative;
    z-index: 1;
}

.stat-label {
    font-size: 13px;
    opacity: 0.85;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    position: relative;
    z-index: 1;
}

.welcome-section {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h2 {
    color: white;
    margin: 0;
    font-size: 28px;
}

.quick-actions {
    margin-top: 30px;
}

.quick-actions h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
}

/* Collapsible sections */
.collapsible-section {
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    background: white;
}

.collapsible-header {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s;
}

.collapsible-header:hover {
    background: linear-gradient(135deg, #3b82f6, #1e3a8a);
}

.collapsible-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
}

.collapsible-toggle {
    font-size: 20px;
    transition: transform 0.3s;
}

.collapsible-content {
    padding: 20px;
    display: none;
}

.collapsible-content.active {
    display: block;
}

.collapsible-section.active .collapsible-toggle {
    transform: rotate(180deg);
}

/* Stats overview always visible */
.stats-overview {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    padding: 32px;
    border-radius: 16px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
}

.stats-overview::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.stats-overview h3 {
    color: white;
    margin-top: 0;
    margin-bottom: 24px;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.5px;
    position: relative;
    z-index: 1;
}

/* Sidebar Navigation Styles */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    transition: transform 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    transform: translateX(-280px);
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-logo {
    height: 36px;
    width: auto;
    filter: brightness(0) invert(1);
}

.sidebar-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.sidebar-nav {
    padding: 16px 0;
}

.nav-section {
    margin-bottom: 24px;
}

.nav-section h4 {
    color: rgba(255, 255, 255, 0.6);
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    margin: 0 20px 12px;
    font-weight: 600;
    padding: 8px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 14px 20px;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    margin: 2px 0;
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.12);
    color: white;
    border-left-color: #60a5fa;
    padding-left: 24px;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.18);
    color: white;
    border-left-color: #60a5fa;
    font-weight: 600;
}

.nav-item.logout:hover {
    background: rgba(239, 68, 68, 0.15);
    border-left-color: #f87171;
    color: #fecaca;
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    opacity: 0.9;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* Professional Icon Styles */
.icon-home::before { content: "⌂"; }
.icon-user::before { content: "👤"; }
.icon-users::before { content: "👥"; }
.icon-chart::before { content: "📊"; }
.icon-settings::before { content: "⚙"; }
.icon-logout::before { content: "⏻"; }
.icon-leave::before { content: "📋"; }
.icon-calendar::before { content: "📅"; }
.icon-money::before { content: "💰"; }
.icon-star::before { content: "⭐"; }
.icon-briefcase::before { content: "💼"; }
.icon-clock::before { content: "⏰"; }
.icon-document::before { content: "📄"; }
.icon-phone::before { content: "📞"; }
.icon-book::before { content: "📚"; }
.icon-trophy::before { content: "🏆"; }
.icon-target::before { content: "🎯"; }
.icon-shield::before { content: "🛡"; }
.icon-database::before { content: "💾"; }

/* Alternative clean icon approach using CSS shapes */
.nav-icon.clean {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    width: 20px;
    height: 20px;
}

.nav-icon.clean.home::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 10px;
    border: 2px solid currentColor;
    border-top: none;
    border-radius: 0 0 2px 2px;
}

.nav-icon.clean.home::before {
    content: "";
    position: absolute;
    top: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid currentColor;
}

/* Main Content Layout */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 0;
}

.page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 24px 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: #1e3a8a;
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    padding: 12px 16px;
    background: rgba(30, 58, 138, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(30, 58, 138, 0.1);
}

.user-info span:first-child {
    font-weight: 600;
    color: #1e3a8a;
    font-size: 14px;
}

.user-role {
    font-size: 11px;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    font-weight: 600;
    background: #e2e8f0;
    padding: 2px 8px;
    border-radius: 12px;
}

.content-wrapper {
    padding: 32px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Enhanced Stats Cards */
.stat-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

/* Register Form Styles */
.register-form {
    margin-top: 24px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 8px;
    font-size: 14px;
    letter-spacing: 0.3px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #ffffff;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: #fafbff;
}

.show-pass {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 13px;
    color: #64748b;
}

.show-pass input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.register-btn {
    width: 100%;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    margin-top: 32px;
}

.register-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.success-message {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-280px);
    }

    .sidebar.collapsed {
        transform: translateX(-280px);
    }

    .main-content {
        margin-left: 0;
    }

    .content-wrapper {
        padding: 20px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .user-info {
        align-items: flex-start;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .register-form {
        margin-top: 16px;
    }

    .form-group {
        margin-bottom: 20px;
    }
}

/* Report Results Styles */
.report-results {
    margin-top: 24px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.report-results h4 {
    margin: 0 0 16px 0;
    color: #1e3a8a;
    font-size: 16px;
    font-weight: 600;
}

.report-table {
    overflow-x: auto;
}

.report-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.report-table th {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.3px;
}

.report-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    color: #374151;
    font-size: 14px;
}

.report-table tr:last-child td {
    border-bottom: none;
}

.report-table tr:hover {
    background: #f8fafc;
}

.card-button {
    width: 100%;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.card-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

/* Mobile responsiveness for reports */
@media (max-width: 768px) {
    .report-table {
        font-size: 12px;
    }

    .report-table th,
    .report-table td {
        padding: 8px 12px;
    }

    .report-results {
        padding: 16px;
        margin-top: 16px;
    }
}
