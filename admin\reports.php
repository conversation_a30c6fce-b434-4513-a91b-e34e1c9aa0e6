<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }

// Handle report generation
if(isset($_POST['generate_user_report'])) {
    $users_data = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $user_report_generated = true;
}

if(isset($_POST['generate_attendance_report'])) {
    $attendance_data = $conn->query("SELECT DATE(created_at) as date, COUNT(*) as total_records FROM attendance_records GROUP BY DATE(created_at) ORDER BY date DESC LIMIT 10");
    $attendance_report_generated = true;
}

if(isset($_POST['generate_payroll_report'])) {
    $payroll_data = $conn->query("SELECT status, COUNT(*) as count, SUM(amount) as total_amount FROM payroll GROUP BY status");
    $payroll_report_generated = true;
}

if(isset($_POST['generate_performance_report'])) {
    $performance_data = $conn->query("SELECT rating, COUNT(*) as count FROM performance_reviews GROUP BY rating ORDER BY rating DESC");
    $performance_report_generated = true;
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Reports - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>
    
    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>MAIN</h4>
            <a href="../dashboard/admin.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>
        
        <div class="nav-section">
            <h4>USER MANAGEMENT</h4>
            <a href="../auth/register.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <span class="nav-text">Create User</span>
            </a>
            <a href="manage_users.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v4h2v-7.5c0-.83.67-1.5 1.5-1.5S12 9.67 12 10.5V18h2v-4h3v4h2V9.5c0-1.93-1.57-3.5-3.5-3.5S12 7.57 12 9.5V18H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Manage Users</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>SYSTEM</h4>
            <a href="reports.php" class="nav-item active">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                </div>
                <span class="nav-text">Reports</span>
            </a>
            <a href="settings.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                </div>
                <span class="nav-text">Settings</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>ACCOUNT</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                </div>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>System Reports</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">Administrator</span>
            </div>
        </div>
    </header>
    
    <div class="content-wrapper">
        <div class="dashboard-grid">
            <!-- User Reports Card -->
            <div class="dashboard-card">
                <div class="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <div class="card-title">User Reports</div>
                <div class="card-description">Generate comprehensive user activity and management reports</div>

                <?php if(isset($user_report_generated)): ?>
                    <div class="report-results">
                        <h4>User Distribution Report</h4>
                        <div class="report-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Role</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($row = $users_data->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $row['role'] ?></td>
                                            <td><?= $row['count'] ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <form method="POST" style="margin-top: 16px;">
                    <button type="submit" name="generate_user_report" class="card-button">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        Generate Report
                    </button>
                </form>
            </div>

            <!-- Attendance Reports Card -->
            <div class="dashboard-card">
                <div class="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                    </svg>
                </div>
                <div class="card-title">Attendance Reports</div>
                <div class="card-description">View detailed attendance statistics and trends</div>

                <?php if(isset($attendance_report_generated)): ?>
                    <div class="report-results">
                        <h4>Recent Attendance Summary</h4>
                        <div class="report-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Records</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($attendance_data->num_rows > 0): ?>
                                        <?php while($row = $attendance_data->fetch_assoc()): ?>
                                            <tr>
                                                <td><?= date('M d, Y', strtotime($row['date'])) ?></td>
                                                <td><?= $row['total_records'] ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="2">No attendance records found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <form method="POST" style="margin-top: 16px;">
                    <button type="submit" name="generate_attendance_report" class="card-button">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        View Reports
                    </button>
                </form>
            </div>

            <!-- Payroll Reports Card -->
            <div class="dashboard-card">
                <div class="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                </div>
                <div class="card-title">Payroll Reports</div>
                <div class="card-description">Generate payroll summaries and financial reports</div>

                <?php if(isset($payroll_report_generated)): ?>
                    <div class="report-results">
                        <h4>Payroll Status Summary</h4>
                        <div class="report-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Count</th>
                                        <th>Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($payroll_data->num_rows > 0): ?>
                                        <?php while($row = $payroll_data->fetch_assoc()): ?>
                                            <tr>
                                                <td><?= $row['status'] ?></td>
                                                <td><?= $row['count'] ?></td>
                                                <td>$<?= number_format($row['total_amount'], 2) ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3">No payroll records found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <form method="POST" style="margin-top: 16px;">
                    <button type="submit" name="generate_payroll_report" class="card-button">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                        </svg>
                        Generate Report
                    </button>
                </form>
            </div>

            <!-- Performance Reports Card -->
            <div class="dashboard-card">
                <div class="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z"/>
                    </svg>
                </div>
                <div class="card-title">Performance Reports</div>
                <div class="card-description">Analyze employee performance metrics and reviews</div>

                <?php if(isset($performance_report_generated)): ?>
                    <div class="report-results">
                        <h4>Performance Rating Distribution</h4>
                        <div class="report-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Rating</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($performance_data->num_rows > 0): ?>
                                        <?php while($row = $performance_data->fetch_assoc()): ?>
                                            <tr>
                                                <td><?= $row['rating'] ?>/5</td>
                                                <td><?= $row['count'] ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="2">No performance reviews found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <form method="POST" style="margin-top: 16px;">
                    <button type="submit" name="generate_performance_report" class="card-button">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z"/>
                        </svg>
                        View Analytics
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
