<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }
?>
<!DOCTYPE html>
<html>
<head>
<title>Settings - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>
    
    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>MAIN</h4>
            <a href="../dashboard/admin.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>
        
        <div class="nav-section">
            <h4>SYSTEM</h4>
            <a href="reports.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                </div>
                <span class="nav-text">Reports</span>
            </a>
            <a href="settings.php" class="nav-item active">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                </div>
                <span class="nav-text">Settings</span>
            </a>
        </div>
        
        <div class="nav-section">
            <h4>ACCOUNT</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                </div>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>System Settings</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">Administrator</span>
            </div>
        </div>
    </header>
    
    <div class="content-wrapper">
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>🏢 Company Settings</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-card">
                    <form method="POST">
                        <div class="card-title">Company Information</div>
                        <input type="text" name="company_name" placeholder="Company Name" value="WorkforceWise Inc.">
                        <input type="email" name="company_email" placeholder="Company Email" value="<EMAIL>">
                        <input type="text" name="company_phone" placeholder="Company Phone" value="+****************">
                        <textarea name="company_address" placeholder="Company Address">123 Business Street, Suite 100
City, State 12345</textarea>
                        <button type="submit" name="update_company">Update Company Info</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>🔐 Security Settings</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-title">Password Policy</div>
                        <div class="card-description">Configure password requirements</div>
                        <form method="POST">
                            <label>
                                <input type="checkbox" checked> Require uppercase letters
                            </label>
                            <label>
                                <input type="checkbox" checked> Require numbers
                            </label>
                            <label>
                                <input type="checkbox"> Require special characters
                            </label>
                            <input type="number" name="min_length" placeholder="Minimum Length" value="8">
                            <button type="submit" name="update_password_policy">Update Policy</button>
                        </form>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-title">Session Settings</div>
                        <div class="card-description">Configure user session timeouts</div>
                        <form method="POST">
                            <input type="number" name="session_timeout" placeholder="Session Timeout (minutes)" value="30">
                            <label>
                                <input type="checkbox" checked> Remember login
                            </label>
                            <button type="submit" name="update_session">Update Settings</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📧 Email Settings</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-card">
                    <div class="card-title">SMTP Configuration</div>
                    <form method="POST">
                        <input type="text" name="smtp_host" placeholder="SMTP Host" value="smtp.gmail.com">
                        <input type="number" name="smtp_port" placeholder="SMTP Port" value="587">
                        <input type="email" name="smtp_username" placeholder="SMTP Username">
                        <input type="password" name="smtp_password" placeholder="SMTP Password">
                        <label>
                            <input type="checkbox" checked> Enable TLS
                        </label>
                        <button type="submit" name="update_smtp">Update SMTP Settings</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');
    
    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
