<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Debug flag - set to false in production
$debug = true;

if($debug) {
    echo "<!-- DEBUG: Starting login process -->\n";
}

// Include database connection with error handling
try {
    include "../config/db.php";
    if($debug) {
        echo "<!-- DEBUG: Database connection included -->\n";
    }
} catch (Exception $e) {
    $error = "Database connection failed: " . $e->getMessage();
    if($debug) {
        echo "<!-- DEBUG: Database error: " . $e->getMessage() . " -->\n";
    }
}

if(isset($_POST['login'])){
    if($debug) {
        echo "<!-- DEBUG: Login form submitted -->\n";
        echo "<!-- DEBUG: Username: " . htmlspecialchars($_POST['username']) . " -->\n";
    }

    $username = $_POST['username'];
    $password = md5($_POST['password']);

    // Check if database connection exists
    if(!isset($conn) || $conn->connect_error) {
        $error = "Database connection failed: " . (isset($conn) ? $conn->connect_error : "Connection not established");
        if($debug) {
            echo "<!-- DEBUG: Database connection error: $error -->\n";
        }
    } else {
        if($debug) {
            echo "<!-- DEBUG: Database connection successful -->\n";
        }

        // Use prepared statement for security
        $stmt = $conn->prepare("SELECT * FROM users WHERE username = ? AND password = ?");
        if($stmt) {
            $stmt->bind_param("ss", $username, $password);
            $stmt->execute();
            $result = $stmt->get_result();

            if($debug) {
                echo "<!-- DEBUG: Query executed, rows found: " . $result->num_rows . " -->\n";
            }

            if($result->num_rows > 0){
                $user = $result->fetch_assoc();
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];

                if($debug) {
                    echo "<!-- DEBUG: User found - Role: " . $user['role'] . " -->\n";
                }

                switch($user['role']){
                    case 'Admin':
                        header("Location: ../dashboard/admin.php");
                        exit;
                    case 'HR Head':
                        header("Location: ../dashboard/hr_head.php");
                        exit;
                    case 'HR Staff':
                        header("Location: ../dashboard/hr_staff.php");
                        exit;
                    case 'Employee':
                        header("Location: ../dashboard/employee.php");
                        exit;
                    default:
                        $error = "Invalid user role: " . $user['role'];
                }
            } else {
                $error = "Invalid username or password";
                if($debug) {
                    echo "<!-- DEBUG: No matching user found -->\n";
                }
            }
            $stmt->close();
        } else {
            $error = "Database query failed: " . $conn->error;
            if($debug) {
                echo "<!-- DEBUG: Prepared statement failed: " . $conn->error . " -->\n";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - HR System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-500) 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center;">

    <div class="login-container" style="width: 100%; max-width: 400px; padding: var(--space-6);">
        <!-- Login Card -->
        <div class="card" style="margin: 0; width: 100%; box-shadow: var(--shadow-xl); border: none;">
            <div class="card-content" style="text-align: center;">
                <!-- Logo -->
                <div style="width: 80px; height: 80px; background: var(--primary-600); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-6) auto; color: white; font-size: 2rem; font-weight: 700;">
                    HR
                </div>

                <!-- Title -->
                <h1 style="color: var(--gray-900); margin-bottom: var(--space-2); font-size: 1.875rem;">Welcome Back</h1>
                <p style="color: var(--gray-600); margin-bottom: var(--space-8); font-size: 0.875rem;">Sign in to your HR System account</p>

                <!-- Error Message -->
                <?php if(isset($error)): ?>
                    <div class="alert alert-error" style="text-align: left; margin-bottom: var(--space-6);">
                        <span style="font-size: 1.25rem;">⚠️</span>
                        <div>
                            <strong>Login Failed</strong><br>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Login Form -->
                <form method="POST" style="text-align: left;">
                    <div class="form-group">
                        <label class="form-label required" for="username">Username</label>
                        <input type="text"
                               id="username"
                               name="username"
                               class="form-input"
                               placeholder="Enter your username"
                               required
                               autocomplete="username"
                               value="<?= isset($_POST['username']) ? htmlspecialchars($_POST['username']) : '' ?>">
                    </div>

                    <div class="form-group">
                        <label class="form-label required" for="password">Password</label>
                        <input type="password"
                               id="password"
                               name="password"
                               class="form-input"
                               placeholder="Enter your password"
                               required
                               autocomplete="current-password">
                    </div>

                    <button type="submit" name="login" class="btn btn-primary btn-lg w-full" style="margin-top: var(--space-6);">
                        <span>Sign In</span>
                    </button>
                </form>

                <!-- Footer -->
                <div style="margin-top: var(--space-8); padding-top: var(--space-6); border-top: 1px solid var(--gray-200); text-align: center;">
                    <p style="color: var(--gray-500); font-size: 0.75rem; margin: 0;">
                        HR Management System v2.0<br>
                        Secure • Modern • Efficient
                    </p>
                </div>
            </div>
        </div>

        <!-- Demo Credentials -->
        <div class="card" style="margin-top: var(--space-4); width: 100%; background: rgba(255, 255, 255, 0.95);">
            <div class="card-content">
                <h3 style="color: var(--gray-700); margin-bottom: var(--space-3); font-size: 0.875rem; text-transform: uppercase; letter-spacing: 0.05em;">Demo Credentials</h3>
                <div style="font-size: 0.75rem; color: var(--gray-600); line-height: 1.5;">
                    <strong>Admin:</strong> admin / admin123<br>
                    <strong>HR Head:</strong> hrhead / hr123<br>
                    <strong>HR Staff:</strong> hrstaff / hr123<br>
                    <strong>Employee:</strong> employee / emp123
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            button.innerHTML = '<div class="spinner" style="margin-right: var(--space-2);"></div> Signing in...';
            button.disabled = true;

            // Re-enable button after 5 seconds in case of slow response
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 5000);
        });

        // Focus on username field when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
