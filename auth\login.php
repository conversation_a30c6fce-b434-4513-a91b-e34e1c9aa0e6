<?php
session_start();
include "../config/db.php";  // Make sure this file defines $conn

if(isset($_POST['login'])){
    $username = $_POST['username'];
    $password = md5($_POST['password']); // using MD5 for now

    // Use prepared statement to prevent SQL injection
    $stmt = $conn->prepare("SELECT * FROM users WHERE username = ? AND password = ?");
    $stmt->bind_param("ss", $username, $password);
    $stmt->execute();
    $result = $stmt->get_result();

    if($result->num_rows > 0){
        $user = $result->fetch_assoc();
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];

        switch($user['role']){
            case 'Admin': header("Location: ../dashboard/admin.php"); break;
            case 'HR Head': header("Location: ../dashboard/hr_head.php"); break;
            case 'HR Staff': header("Location: ../dashboard/hr_staff.php"); break;
            case 'Employee': header("Location: ../dashboard/employee.php"); break;
        }
        exit();
    } else {
        $error = "Invalid username or password";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Login</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f5fb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .container {
            width: 400px;
            background: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(30, 58, 138, 0.2);
            text-align: center;
        }

        
       
        input {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #1e3a8a;
            border-radius: 8px;
            box-sizing: border-box;
        }

        button {
            width: 100%;
            padding: 12px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: 0.3s;
        }

        button:hover { background-color: #3b82f6; }

        .error {
            color: red;
            margin-bottom: 10px;
        }

        .show-pass {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 8px;
            margin-bottom: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- WorkforceWise Logo -->
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="logo">

        <?php if(isset($error)) echo "<div class='error'>$error</div>"; ?>
        <form method="POST">
            <input type="text" name="username" placeholder="Username" required>
            <input type="password" id="password" name="password" placeholder="Password" required>
            <div class="show-pass">
                <input type="checkbox" onclick="togglePassword()"> Show Password
            </div>
            <button type="submit" name="login">Login</button>
        </form>
    </div>

    <script>
        function togglePassword() {
            const pass = document.getElementById('password');
            pass.type = pass.type === 'password' ? 'text' : 'password';
        }
    </script>
</body>
</html>

