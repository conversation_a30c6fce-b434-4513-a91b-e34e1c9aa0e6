<?php
session_start();
include "../config/db.php";

if(isset($_POST['login'])){
    $username = $_POST['username'];
    $password = md5($_POST['password']);
    
    $sql = "SELECT * FROM users WHERE username='$username' AND password='$password'";
    $result = $conn->query($sql);
    
    if($result->num_rows > 0){
        $user = $result->fetch_assoc();
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        
        switch($user['role']){
            case 'Admin': header("Location: ../dashboard/admin.php"); break;
            case 'HR Head': header("Location: ../dashboard/hr_head.php"); break;
            case 'HR Staff': header("Location: ../dashboard/hr_staff.php"); break;
            case 'Employee': header("Location: ../dashboard/employee.php"); break;
        }
    } else {
        $error = "Invalid username or password";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - HR System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-500) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: var(--font-sans);
        }

        .login-wrapper {
            width: 100%;
            max-width: 400px;
            padding: var(--space-6);
        }

        .login-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            padding: var(--space-8);
            border: none;
            text-align: center;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: var(--primary-600);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6) auto;
            color: white;
            font-size: 2rem;
            font-weight: 700;
        }

        .login-title {
            color: var(--gray-900);
            margin-bottom: var(--space-2);
            font-size: 1.875rem;
            font-weight: 700;
        }

        .login-subtitle {
            color: var(--gray-600);
            margin-bottom: var(--space-8);
            font-size: 0.875rem;
        }

        .error-message {
            background: var(--error-50);
            border: 1px solid var(--error-200);
            color: var(--error-800);
            padding: var(--space-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-6);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            text-align: left;
        }

        .login-form {
            text-align: left;
        }

        .form-group {
            margin-bottom: var(--space-5);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: var(--space-2);
        }

        .form-input {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
            color: var(--gray-900);
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .login-button {
            width: 100%;
            background: var(--primary-600);
            color: white;
            border: none;
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: var(--space-2);
        }

        .login-button:hover {
            background: var(--primary-700);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .login-footer {
            margin-top: var(--space-8);
            padding-top: var(--space-6);
            border-top: 1px solid var(--gray-200);
            text-align: center;
        }

        .demo-credentials {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-top: var(--space-4);
            border: 1px solid var(--gray-200);
        }

        .demo-title {
            color: var(--gray-700);
            margin-bottom: var(--space-3);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .demo-list {
            font-size: 0.75rem;
            color: var(--gray-600);
            line-height: 1.5;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        <!-- Login Card -->
        <div class="login-card">
            <!-- Logo -->
            <div class="login-logo">HR</div>

            <!-- Title -->
            <h1 class="login-title">Welcome Back</h1>
            <p class="login-subtitle">Sign in to your HR System account</p>

            <!-- Error Message -->
            <?php if(isset($error)): ?>
                <div class="error-message">
                    <span style="font-size: 1.25rem;">⚠️</span>
                    <div>
                        <strong>Login Failed:</strong> <?= htmlspecialchars($error) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <form method="POST" class="login-form">
                <div class="form-group">
                    <label class="form-label" for="username">Username</label>
                    <input type="text"
                           id="username"
                           name="username"
                           class="form-input"
                           placeholder="Enter your username"
                           required
                           autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <input type="password"
                           id="password"
                           name="password"
                           class="form-input"
                           placeholder="Enter your password"
                           required
                           autocomplete="current-password">
                </div>

                <button type="submit" name="login" class="login-button">
                    Sign In
                </button>
            </form>

            <!-- Footer -->
            <div class="login-footer">
                <p style="color: var(--gray-500); font-size: 0.75rem; margin: 0;">
                    HR Management System v2.0<br>
                    Secure • Modern • Efficient
                </p>
            </div>
        </div>

        <!-- Demo Credentials -->
        <div class="demo-credentials">
            <div class="demo-title">Demo Credentials</div>
            <div class="demo-list">
                <strong>Admin:</strong> admin / admin123<br>
                <strong>HR Head:</strong> hrhead / hrhead123<br>
                <strong>HR Staff:</strong> hrstaff / hrstaff123<br>
                <strong>Employee:</strong> employee1 / emp123
            </div>
        </div>
    </div>

    <script>
        // Focus on username field when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.textContent;

            button.textContent = 'Signing in...';
            button.disabled = true;

            // Re-enable button after 5 seconds in case of slow response
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
            }, 5000);
        });
    </script>
</body>
</html>
