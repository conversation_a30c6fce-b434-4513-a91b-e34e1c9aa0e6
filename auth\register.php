<?php
session_start();
include "../config/db.php";

// Only Admin can register
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }

if(isset($_POST['register'])){
    $username = $_POST['username'];
    $password = md5($_POST['password']);
    $role = $_POST['role'];

    $conn->query("INSERT INTO users (username,password,role) VALUES ('$username','$password','$role')");
    $msg = "User created successfully!";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register New User - HR System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <?php include "../includes/header.php"; ?>

    <main class="main-content">
        <div class="content-wrapper">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container">
                    <div class="page-header-content">
                        <div>
                            <nav class="breadcrumb">
                                <span class="breadcrumb-item">Admin</span>
                                <span class="breadcrumb-separator">›</span>
                                <span class="breadcrumb-item">User Management</span>
                                <span class="breadcrumb-separator">›</span>
                                <span class="breadcrumb-item">Register User</span>
                            </nav>
                            <h1 class="page-title">Register New User</h1>
                            <p class="page-subtitle">Create a new user account for the HR system</p>
                        </div>
                        <div class="page-actions">
                            <a href="../dashboard/admin.php" class="btn btn-outline">
                                <span>←</span>
                                Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container">
                <div class="content-grid">
                    <!-- Registration Form -->
                    <div class="section">
                        <div class="section-header">
                            <h2 class="section-title">User Information</h2>
                            <p class="section-description">Enter the details for the new user account</p>
                        </div>
                        <div class="section-content">
                            <!-- Success Message -->
                            <?php if(isset($msg)): ?>
                                <div class="alert alert-success">
                                    <span>✅</span>
                                    <div>
                                        <strong>Success!</strong><br>
                                        <?= htmlspecialchars($msg) ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <form method="POST" id="registerForm">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required" for="username">Username</label>
                                        <input type="text"
                                               id="username"
                                               name="username"
                                               class="form-input"
                                               placeholder="Enter username"
                                               required
                                               autocomplete="username"
                                               value="<?= isset($_POST['username']) ? htmlspecialchars($_POST['username']) : '' ?>">
                                        <div class="form-help">Username must be unique and at least 3 characters long</div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label required" for="role">Role</label>
                                        <select id="role" name="role" class="form-input form-select" required>
                                            <option value="">Select Role</option>
                                            <option value="Admin" <?= (isset($_POST['role']) && $_POST['role'] === 'Admin') ? 'selected' : '' ?>>Admin</option>
                                            <option value="HR Head" <?= (isset($_POST['role']) && $_POST['role'] === 'HR Head') ? 'selected' : '' ?>>HR Head</option>
                                            <option value="HR Staff" <?= (isset($_POST['role']) && $_POST['role'] === 'HR Staff') ? 'selected' : '' ?>>HR Staff</option>
                                            <option value="Employee" <?= (isset($_POST['role']) && $_POST['role'] === 'Employee') ? 'selected' : '' ?>>Employee</option>
                                        </select>
                                        <div class="form-help">Select the appropriate role for this user</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label required" for="password">Password</label>
                                    <input type="password"
                                           id="password"
                                           name="password"
                                           class="form-input"
                                           placeholder="Enter password"
                                           required
                                           autocomplete="new-password"
                                           minlength="6">
                                    <div class="form-help">Password must be at least 6 characters long</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label required" for="confirm_password">Confirm Password</label>
                                    <input type="password"
                                           id="confirm_password"
                                           name="confirm_password"
                                           class="form-input"
                                           placeholder="Confirm password"
                                           required
                                           autocomplete="new-password"
                                           minlength="6">
                                    <div class="form-help">Re-enter the password to confirm</div>
                                </div>

                                <div style="margin-top: var(--space-8);">
                                    <button type="submit" name="register" class="btn btn-primary btn-lg">
                                        <span>👥</span>
                                        Create User Account
                                    </button>
                                    <a href="../dashboard/admin.php" class="btn btn-ghost btn-lg" style="margin-left: var(--space-3);">
                                        Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Role Information -->
                    <div class="section">
                        <div class="section-header">
                            <h2 class="section-title">Role Permissions</h2>
                            <p class="section-description">Understanding user roles and their capabilities</p>
                        </div>
                        <div class="section-content">
                            <div style="margin-bottom: var(--space-4);">
                                <h4 style="color: var(--primary-600); margin-bottom: var(--space-2);">🛡️ Admin</h4>
                                <p style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: var(--space-3);">
                                    Full system access including user management, system configuration, and all HR functions.
                                </p>
                            </div>

                            <div style="margin-bottom: var(--space-4);">
                                <h4 style="color: var(--primary-600); margin-bottom: var(--space-2);">👔 HR Head</h4>
                                <p style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: var(--space-3);">
                                    Senior HR role with access to all HR functions, reporting, and strategic HR management.
                                </p>
                            </div>

                            <div style="margin-bottom: var(--space-4);">
                                <h4 style="color: var(--primary-600); margin-bottom: var(--space-2);">💼 HR Staff</h4>
                                <p style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: var(--space-3);">
                                    Standard HR role with access to employee management, leave processing, and recruitment.
                                </p>
                            </div>

                            <div style="margin-bottom: var(--space-4);">
                                <h4 style="color: var(--primary-600); margin-bottom: var(--space-2);">👨‍💼 Employee</h4>
                                <p style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: var(--space-3);">
                                    Standard employee access to personal information, leave applications, and self-service features.
                                </p>
                            </div>

                            <div class="alert alert-info">
                                <span>💡</span>
                                <div>
                                    <strong>Security Note</strong><br>
                                    Always assign the minimum required role for each user to maintain system security.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();

                // Show error styling
                const confirmField = document.getElementById('confirm_password');
                confirmField.classList.add('error');

                // Create or update error message
                let errorMsg = confirmField.parentNode.querySelector('.form-error');
                if (!errorMsg) {
                    errorMsg = document.createElement('div');
                    errorMsg.className = 'form-error';
                    confirmField.parentNode.appendChild(errorMsg);
                }
                errorMsg.innerHTML = '<span>⚠️</span> Passwords do not match';

                // Remove error styling after user starts typing
                confirmField.addEventListener('input', function() {
                    this.classList.remove('error');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });

                return false;
            }

            // Add loading state to button
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            button.innerHTML = '<div class="spinner" style="margin-right: var(--space-2);"></div> Creating User...';
            button.disabled = true;

            // Re-enable button after 5 seconds in case of slow response
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 5000);
        });

        // Real-time password confirmation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.classList.add('error');
            } else {
                this.classList.remove('error');
            }
        });
    </script>
</body>
</html>
