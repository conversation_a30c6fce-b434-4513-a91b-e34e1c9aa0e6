<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

// Get HR Head stats
$pending_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE status='Pending'")->fetch_assoc()['count'];
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$pending_payroll = $conn->query("SELECT COUNT(*) as count FROM payroll WHERE status='Draft'")->fetch_assoc()['count'];
$total_applications = $conn->query("SELECT COUNT(*) as count FROM job_applications WHERE status='Pending'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>HR Head Dashboard</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>HR Head Dashboard</header>
<div class="container">
    <div class="welcome-section">
        <h2>Welcome, <?= $_SESSION['username']; ?></h2>
        <p>Manage your HR operations efficiently</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number"><?= $pending_leaves ?></div>
            <div class="stat-label">Pending Leave Requests</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $total_employees ?></div>
            <div class="stat-label">Total Employees</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $pending_payroll ?></div>
            <div class="stat-label">Pending Payroll</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $total_applications ?></div>
            <div class="stat-label">Job Applications</div>
        </div>
    </div>

    <div class="quick-actions">
        <h3>Management Tools</h3>
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">📋</div>
                <div class="card-title">Manage Leaves</div>
                <div class="card-description">Approve or reject leave requests</div>
                <a href="../leave/manage_leaves.php" class="card-link">Manage Leaves</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">💳</div>
                <div class="card-title">Approve Payroll</div>
                <div class="card-description">Review and approve payroll drafts</div>
                <a href="../payroll/approve_payroll.php" class="card-link">Approve Payroll</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">⭐</div>
                <div class="card-title">Performance Reviews</div>
                <div class="card-description">Add employee performance reviews</div>
                <a href="../performance/add_review.php" class="card-link">Add Review</a>
            </div>
            
            <div class="dashboard-card">
                <div class="card-icon">👥</div>
                <div class="card-title">Job Applications</div>
                <div class="card-description">Review candidate applications</div>
                <a href="../recruitment/applications.php" class="card-link">Review Applications</a>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="../auth/logout.php" class="card-link">Logout</a>
    </div>
</div>
</body>
</html>
