<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

// Get HR Head stats
$pending_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE status='Pending'")->fetch_assoc()['count'];
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$pending_payroll = $conn->query("SELECT COUNT(*) as count FROM payroll WHERE status='Draft'")->fetch_assoc()['count'];
$total_applications = $conn->query("SELECT COUNT(*) as count FROM job_applications WHERE status='Pending'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>HR Head Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>MAIN</h4>
            <a href="hr_head.php" class="nav-item active">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>LEAVE MANAGEMENT</h4>
            <a href="../leave/manage_leaves.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V7H5V5H19M5,19V9H19V19H5M6,11H8V13H6V11M6,15H8V17H6V15M10,11H18V13H10V11M10,15H16V17H10V15Z"/>
                    </svg>
                </div>
                <span class="nav-text">Manage Leaves</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>PAYROLL</h4>
            <a href="../payroll/approve_payroll.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                </div>
                <span class="nav-text">Approve Payroll</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>PERFORMANCE</h4>
            <a href="../performance/add_review.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
                    </svg>
                </div>
                <span class="nav-text">Performance Reviews</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>RECRUITMENT</h4>
            <a href="../recruitment/applications.php" class="nav-item">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v4h2v-7.5c0-.83.67-1.5 1.5-1.5S12 9.67 12 10.5V18h2v-4h3v4h2V9.5c0-1.93-1.57-3.5-3.5-3.5S12 7.57 12 9.5V18H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Job Applications</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>ACCOUNT</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <div class="nav-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                </div>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>HR Head Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">HR Head</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 HR Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number"><?= $pending_leaves ?></div>
                    <div class="stat-label">Pending Leave Requests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?= $total_employees ?></div>
                    <div class="stat-label">Total Employees</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💳</div>
                    <div class="stat-number"><?= $pending_payroll ?></div>
                    <div class="stat-label">Pending Payroll</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📄</div>
                    <div class="stat-number"><?= $total_applications ?></div>
                    <div class="stat-label">Job Applications</div>
                </div>
            </div>
        </div>

        <!-- Leave & Attendance Management -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📋 Leave & Attendance Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📋</div>
                        <div class="card-title">Manage Leaves</div>
                        <div class="card-description">Approve or reject leave requests</div>
                        <a href="../leave/manage_leaves.php" class="card-link">Manage Leaves</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">⏰</div>
                        <div class="card-title">Attendance Reports</div>
                        <div class="card-description">View employee attendance reports</div>
                        <a href="../attendance/reports.php" class="card-link">View Reports</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payroll & Performance -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>💳 Payroll & Performance</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">💳</div>
                        <div class="card-title">Approve Payroll</div>
                        <div class="card-description">Review and approve payroll drafts</div>
                        <a href="../payroll/approve_payroll.php" class="card-link">Approve Payroll</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">⭐</div>
                        <div class="card-title">Performance Reviews</div>
                        <div class="card-description">Add employee performance reviews</div>
                        <a href="../performance/add_review.php" class="card-link">Add Review</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recruitment & Training -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>👥 Recruitment & Training</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">👥</div>
                        <div class="card-title">Job Applications</div>
                        <div class="card-description">Review candidate applications</div>
                        <a href="../recruitment/applications.php" class="card-link">Review Applications</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📚</div>
                        <div class="card-title">Training Programs</div>
                        <div class="card-description">Manage employee training</div>
                        <a href="../training/manage_training.php" class="card-link">Manage Training</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">HR Reports</div>
                        <div class="card-description">Generate comprehensive HR reports</div>
                        <a href="../reports/hr_reports.php" class="card-link">View Reports</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Goals & Objectives</div>
                        <div class="card-description">Set and track employee goals</div>
                        <a href="../goals/manage_goals.php" class="card-link">Manage Goals</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
