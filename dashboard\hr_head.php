<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

// Get HR Head stats
$pending_leaves = $conn->query("SELECT COUNT(*) as count FROM leave_requests WHERE status='Pending'")->fetch_assoc()['count'];
$total_employees = $conn->query("SELECT COUNT(*) as count FROM users WHERE role='Employee'")->fetch_assoc()['count'];
$pending_payroll = $conn->query("SELECT COUNT(*) as count FROM payroll WHERE status='Draft'")->fetch_assoc()['count'];
$total_applications = $conn->query("SELECT COUNT(*) as count FROM job_applications WHERE status='Pending'")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html>
<head>
<title>HR Head Dashboard - WorkforceWise</title>
<link rel="stylesheet" href="../assets/style.css">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<!-- Sidebar Navigation -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/workforcewise-logo.svg" alt="WorkforceWise" class="sidebar-logo">
        <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4>Main</h4>
            <a href="hr_head.php" class="nav-item active">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Dashboard</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Leave Management</h4>
            <a href="../leave/manage_leaves.php" class="nav-item">
                <span class="nav-icon">📋</span>
                <span class="nav-text">Manage Leaves</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Payroll</h4>
            <a href="../payroll/approve_payroll.php" class="nav-item">
                <span class="nav-icon">💳</span>
                <span class="nav-text">Approve Payroll</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Performance</h4>
            <a href="../performance/add_review.php" class="nav-item">
                <span class="nav-icon">⭐</span>
                <span class="nav-text">Performance Reviews</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Recruitment</h4>
            <a href="../recruitment/applications.php" class="nav-item">
                <span class="nav-icon">👥</span>
                <span class="nav-text">Job Applications</span>
            </a>
        </div>

        <div class="nav-section">
            <h4>Account</h4>
            <a href="../auth/logout.php" class="nav-item logout">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <header class="page-header">
        <div class="header-content">
            <h1>HR Head Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <?= $_SESSION['username']; ?></span>
                <span class="user-role">HR Head</span>
            </div>
        </div>
    </header>

    <div class="content-wrapper">
        <!-- Stats Overview -->
        <div class="stats-overview">
            <h3>📊 HR Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number"><?= $pending_leaves ?></div>
                    <div class="stat-label">Pending Leave Requests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?= $total_employees ?></div>
                    <div class="stat-label">Total Employees</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💳</div>
                    <div class="stat-number"><?= $pending_payroll ?></div>
                    <div class="stat-label">Pending Payroll</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📄</div>
                    <div class="stat-number"><?= $total_applications ?></div>
                    <div class="stat-label">Job Applications</div>
                </div>
            </div>
        </div>

        <!-- Leave & Attendance Management -->
        <div class="collapsible-section active">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>📋 Leave & Attendance Management</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📋</div>
                        <div class="card-title">Manage Leaves</div>
                        <div class="card-description">Approve or reject leave requests</div>
                        <a href="../leave/manage_leaves.php" class="card-link">Manage Leaves</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">⏰</div>
                        <div class="card-title">Attendance Reports</div>
                        <div class="card-description">View employee attendance reports</div>
                        <a href="../attendance/reports.php" class="card-link">View Reports</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payroll & Performance -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>💳 Payroll & Performance</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">💳</div>
                        <div class="card-title">Approve Payroll</div>
                        <div class="card-description">Review and approve payroll drafts</div>
                        <a href="../payroll/approve_payroll.php" class="card-link">Approve Payroll</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">⭐</div>
                        <div class="card-title">Performance Reviews</div>
                        <div class="card-description">Add employee performance reviews</div>
                        <a href="../performance/add_review.php" class="card-link">Add Review</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recruitment & Training -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection(this)">
                <h3>👥 Recruitment & Training</h3>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">👥</div>
                        <div class="card-title">Job Applications</div>
                        <div class="card-description">Review candidate applications</div>
                        <a href="../recruitment/applications.php" class="card-link">Review Applications</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📚</div>
                        <div class="card-title">Training Programs</div>
                        <div class="card-description">Manage employee training</div>
                        <a href="../training/manage_training.php" class="card-link">Manage Training</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <div class="card-title">HR Reports</div>
                        <div class="card-description">Generate comprehensive HR reports</div>
                        <a href="../reports/hr_reports.php" class="card-link">View Reports</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Goals & Objectives</div>
                        <div class="card-description">Set and track employee goals</div>
                        <a href="../goals/manage_goals.php" class="card-link">Manage Goals</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSection(header) {
    const section = header.parentElement;
    const content = section.querySelector('.collapsible-content');

    section.classList.toggle('active');
    content.classList.toggle('active');
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

// Auto-collapse sidebar on mobile
function checkScreenSize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
}

window.addEventListener('resize', checkScreenSize);
window.addEventListener('load', checkScreenSize);
</script>
</body>
</html>
